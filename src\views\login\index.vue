<template>
  <div class="login-page">
    <!-- 功能介绍区域 -->
    <div class="features-section">
      <div class="features-container">
        <div class="brand-info">
          <div class="brand-logo">
            <img src="/logo.svg" alt="云法务系统" />
            <h1>云法务系统</h1>
          </div>
          <p class="brand-slogan">智能化法务管理，让法律服务更高效</p>
        </div>

        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">
              <el-icon>
                <Folder />
              </el-icon>
            </div>
            <h3>案件管理</h3>
            <p>全流程案件跟踪，从立案到结案的完整管理</p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <el-icon>
                <Document />
              </el-icon>
            </div>
            <h3>合同管理</h3>
            <p>智能合同模板，审批流程，状态跟踪</p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <el-icon>
                <User />
              </el-icon>
            </div>
            <h3>客户管理</h3>
            <p>客户信息维护，关系管理，服务记录</p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <el-icon>
                <Document />
              </el-icon>
            </div>
            <h3>文档管理</h3>
            <p>文档分类存储，在线预览，版本控制</p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <el-icon>
                <Money />
              </el-icon>
            </div>
            <h3>财务管理</h3>
            <p>收支统计，财务报表，数据分析</p>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <el-icon>
                <DataAnalysis />
              </el-icon>
            </div>
            <h3>数据分析</h3>
            <p>业务数据可视化，趋势分析，决策支持</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 登录区域 -->
    <div class="login-container">
      <div class="login-box">
        <div class="login-header">
          <div class="login-logo">
            <img src="/logo.svg" alt="云法务系统" />
          </div>
          <h2>欢迎登录</h2>
          <p>云法务系统管理平台</p>
        </div>

        <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form"
          @keyup.enter="handleLogin">
          <el-form-item prop="username">
            <el-input v-model="loginForm.username" placeholder="请输入用户名" size="large" prefix-icon="User" />
          </el-form-item>

          <el-form-item prop="password">
            <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" size="large" prefix-icon="Lock"
              show-password />
          </el-form-item>

          <el-form-item>
            <el-checkbox v-model="rememberMe">记住我</el-checkbox>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="large" :loading="loading" @click="handleLogin" class="login-btn">
              登录
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 演示账号信息 -->
        <div class="demo-accounts">
          <el-divider>演示账号</el-divider>
          <div class="account-list">
            <div class="account-item" @click="fillAccount('admin', '123456')">
              <span class="account-role">管理员</span>
              <span class="account-info">admin / 123456</span>
            </div>
            <div class="account-item" @click="fillAccount('lawyer', '123456')">
              <span class="account-role">律师</span>
              <span class="account-info">lawyer / 123456</span>
            </div>
            <div class="account-item" @click="fillAccount('assistant', '123456')">
              <span class="account-role">助理</span>
              <span class="account-info">assistant / 123456</span>
            </div>
            <div class="account-item" @click="fillAccount('user', '123456')">
              <span class="account-role">普通用户</span>
              <span class="account-info">user / 123456</span>
            </div>
          </div>
        </div>

        <div class="login-footer">
          <p>&copy; 2024 云法务系统. All rights reserved.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

const loginFormRef = ref<FormInstance>()
const loading = ref(false)
const rememberMe = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    await userStore.login(loginForm)

    // 如果登录时没有获取到用户信息，再次获取
    if (!userStore.userInfo) {
      await userStore.getUserInfo()
    }

    ElMessage.success('登录成功')
    router.push('/')
  } catch (error: any) {
    ElMessage.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

// 填充演示账号
const fillAccount = (username: string, password: string) => {
  loginForm.username = username
  loginForm.password = password
}
</script>

<style lang="scss" scoped>
.login-page {
  height: 100vh;
  display: flex;

  .features-section {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;

    .features-container {
      max-width: 600px;
      color: #fff;

      .brand-info {
        text-align: center;
        margin-bottom: 60px;

        .brand-logo {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 20px;

          img {
            width: 80px;
            height: 80px;
            margin-right: 16px;
          }

          h1 {
            font-size: 36px;
            font-weight: 700;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
          }
        }

        .brand-slogan {
          font-size: 18px;
          opacity: 0.9;
          margin: 0;
          font-weight: 300;
        }
      }

      .features-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;

        .feature-item {
          text-align: center;
          padding: 20px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
          }

          .feature-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 16px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .el-icon {
              font-size: 28px;
              color: #fff;
            }
          }

          h3 {
            font-size: 18px;
            font-weight: 600;
            margin: 0 0 8px 0;
          }

          p {
            font-size: 14px;
            opacity: 0.8;
            margin: 0;
            line-height: 1.5;
          }
        }
      }
    }
  }

  .login-container {
    width: 480px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    padding: 40px;

    .login-box {
      width: 100%;
      max-width: 400px;
      padding: 40px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

      .login-header {
        text-align: center;
        margin-bottom: 30px;

        .login-logo {
          margin-bottom: 20px;

          img {
            width: 64px;
            height: 64px;
          }
        }

        h2 {
          color: #303133;
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
        }

        p {
          color: #909399;
          margin: 0;
          font-size: 14px;
        }
      }

      .login-form {
        .login-btn {
          width: 100%;
          height: 44px;
          font-size: 16px;
          font-weight: 500;
        }
      }

      .demo-accounts {
        margin-top: 20px;

        .el-divider {
          margin: 16px 0;

          :deep(.el-divider__text) {
            color: #909399;
            font-size: 12px;
          }
        }

        .account-list {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 8px;

          .account-item {
            padding: 8px 12px;
            background: rgba(64, 158, 255, 0.1);
            border: 1px solid rgba(64, 158, 255, 0.2);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            flex-direction: column;
            align-items: center;

            &:hover {
              background: rgba(64, 158, 255, 0.2);
              border-color: rgba(64, 158, 255, 0.4);
              transform: translateY(-1px);
            }

            .account-role {
              font-size: 12px;
              color: #409eff;
              font-weight: 500;
              margin-bottom: 2px;
            }

            .account-info {
              font-size: 11px;
              color: #666;
              font-family: 'Courier New', monospace;
            }
          }
        }
      }

      .login-footer {
        text-align: center;
        margin-top: 30px;

        p {
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .login-page {
    .features-section {
      .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }
    }

    .login-container {
      width: 400px;
    }
  }
}

@media (max-width: 768px) {
  .login-page {
    flex-direction: column;

    .features-section {
      flex: none;
      min-height: 60vh;
      padding: 20px;

      .features-container {
        .brand-info {
          margin-bottom: 40px;

          .brand-logo {
            flex-direction: column;

            img {
              margin-right: 0;
              margin-bottom: 12px;
            }

            h1 {
              font-size: 28px;
            }
          }

          .brand-slogan {
            font-size: 16px;
          }
        }

        .features-grid {
          grid-template-columns: 1fr;
          gap: 16px;

          .feature-item {
            padding: 16px;

            .feature-icon {
              width: 50px;
              height: 50px;
              margin-bottom: 12px;

              .el-icon {
                font-size: 24px;
              }
            }

            h3 {
              font-size: 16px;
            }

            p {
              font-size: 13px;
            }
          }
        }
      }
    }

    .login-container {
      width: 100%;
      padding: 20px;

      .login-box {
        padding: 30px 20px;

        .login-header {
          .login-logo img {
            width: 56px;
            height: 56px;
          }

          h2 {
            font-size: 20px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .login-page {
    .features-section {
      min-height: 50vh;
      padding: 15px;

      .features-container {
        .brand-info {
          .brand-logo {
            h1 {
              font-size: 24px;
            }
          }

          .brand-slogan {
            font-size: 14px;
          }
        }
      }
    }

    .login-container {
      padding: 15px;

      .login-box {
        padding: 25px 15px;
      }
    }
  }
}
</style>
