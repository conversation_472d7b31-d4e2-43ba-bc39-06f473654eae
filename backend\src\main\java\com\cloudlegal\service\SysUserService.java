package com.cloudlegal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudlegal.common.PageQuery;
import com.cloudlegal.common.PageResult;
import com.cloudlegal.entity.SysUser;

import java.util.List;
import java.util.Map;

/**
 * 用户服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface SysUserService extends IService<SysUser> {

    /**
     * 根据用户名查询用户
     */
    SysUser getByUsername(String username);

    /**
     * 根据邮箱查询用户
     */
    SysUser getByEmail(String email);

    /**
     * 根据手机号查询用户
     */
    SysUser getByPhone(String phone);

    /**
     * 分页查询用户列表
     */
    PageResult<SysUser> getUserPage(PageQuery pageQuery, Integer status, Long roleId);

    /**
     * 查询用户列表
     */
    List<SysUser> getUserList(String keyword, Integer status, Long roleId);

    /**
     * 创建用户
     */
    boolean createUser(SysUser user);

    /**
     * 更新用户
     */
    boolean updateUser(SysUser user);

    /**
     * 删除用户
     */
    boolean deleteUser(Long userId);

    /**
     * 批量删除用户
     */
    boolean batchDeleteUsers(List<Long> userIds);

    /**
     * 重置用户密码
     */
    boolean resetPassword(Long userId, String newPassword);

    /**
     * 修改用户密码
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 更新用户状态
     */
    boolean updateUserStatus(Long userId, Integer status);

    /**
     * 批量更新用户状态
     */
    boolean batchUpdateUserStatus(List<Long> userIds, Integer status);

    /**
     * 更新用户最后登录信息
     */
    boolean updateLastLoginInfo(Long userId, String loginIp);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 获取用户统计信息
     */
    Map<String, Object> getUserStats();

    /**
     * 根据角色ID查询用户数量
     */
    Long countUsersByRoleId(Long roleId);

    /**
     * 根据部门ID查询用户列表
     */
    List<SysUser> getUsersByDeptId(Long deptId);
}
