@echo off
echo ========================================
echo CORS 跨域问题快速修复工具
echo ========================================

echo.
echo 正在检查和修复CORS配置...
echo.

echo 1. 检查前端服务状态
curl -s http://localhost:8090 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 前端服务未启动，请先运行: npm run dev
    echo.
) else (
    echo ✅ 前端服务正常运行 (端口8090)
)

echo.
echo 2. 检查后端服务状态
curl -s http://localhost:8080/api/test/cors/health >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 后端服务未启动或CORS测试接口不可用
    echo    请先启动后端服务: cd backend && mvn spring-boot:run
    echo.
) else (
    echo ✅ 后端服务正常运行 (端口8080)
)

echo.
echo 3. 测试CORS配置
echo 正在测试跨域请求...

curl -s -X GET ^
  -H "Origin: http://localhost:8090" ^
  -H "Content-Type: application/json" ^
  http://localhost:8080/api/test/cors/get >nul 2>&1

if %errorlevel% neq 0 (
    echo ❌ CORS请求失败
    echo.
    echo 🔧 建议的修复步骤:
    echo    1. 确保后端SecurityConfig包含前端地址
    echo    2. 检查CorsConfig配置是否正确
    echo    3. 重启后端服务
    echo    4. 清除浏览器缓存
    echo.
    echo 📋 详细诊断请运行: node scripts/cors-diagnosis.js
    echo 🌐 测试页面: http://localhost:8090/#/test/cors-test
) else (
    echo ✅ CORS配置正常
    echo.
    echo 🎉 跨域问题已解决！
    echo 📋 如需详细测试，请访问: http://localhost:8090/#/test/cors-test
)

echo.
echo ========================================
echo 修复完成
echo ========================================

pause
