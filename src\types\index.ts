// 用户相关类型
export interface User {
  id: string
  username: string
  realName: string
  email: string
  phone: string
  avatar?: string
  role: string
  department: string
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}

// 案件相关类型
export interface Case {
  id: string
  caseNumber: string
  title: string
  type: string
  status: 'pending' | 'processing' | 'completed' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  clientId: string
  clientName: string
  lawyerId: string
  lawyerName: string
  description: string
  amount?: number
  startDate: string
  endDate?: string
  createdAt: string
  updatedAt: string
}

// 合同相关类型
export interface Contract {
  id: string
  contractNumber: string
  title: string
  type: string
  status: 'draft' | 'reviewing' | 'approved' | 'signed' | 'expired'
  clientId: string
  clientName: string
  amount: number
  signDate?: string
  startDate: string
  endDate: string
  createdAt: string
  updatedAt: string
}

// 客户相关类型
export interface Client {
  id: string
  name: string
  type: 'individual' | 'company'
  contact: string
  phone: string
  email: string
  address: string
  industry?: string
  description?: string
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}

// 文档相关类型
export interface Document {
  id: string
  name: string
  type: string
  size: number
  url: string
  caseId?: string
  contractId?: string
  uploaderId: string
  uploaderName: string
  createdAt: string
}

// 财务相关类型
export interface FinanceRecord {
  id: string
  type: 'income' | 'expense'
  category: string
  amount: number
  description: string
  caseId?: string
  contractId?: string
  date: string
  createdAt: string
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页类型
export interface Pagination {
  current: number
  pageSize: number
  total: number
}

// 表格查询参数
export interface TableQuery {
  page: number
  size: number
  keyword?: string
  [key: string]: any
}
