// 用户相关类型
export interface User {
  id: string
  username: string
  realName: string
  email: string
  phone: string
  avatar?: string
  role: string
  department: string
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}

// 案件相关类型
export interface Case {
  id: string
  caseNumber: string
  title: string
  type: string
  status: 'pending' | 'processing' | 'completed' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  clientId: string
  clientName: string
  lawyerId: string
  lawyerName: string
  description: string
  amount?: number
  startDate: string
  endDate?: string
  createdAt: string
  updatedAt: string
}

// 合同相关类型
export interface Contract {
  id: string
  contractNumber: string
  title: string
  type: string
  status: 'draft' | 'reviewing' | 'approved' | 'signed' | 'expired'
  clientId: string
  clientName: string
  amount: number
  signDate?: string
  startDate: string
  endDate: string
  createdAt: string
  updatedAt: string
}

// 客户相关类型
export interface Client {
  id: number
  clientName: string
  clientType: number // 1-个人，2-企业，3-组织
  contactPerson: string
  phone: string
  email: string
  address: string
  industry?: string
  creditCode?: string // 统一社会信用代码
  legalPerson?: string // 法定代表人
  registeredCapital?: number // 注册资本
  status: number // 0-停用，1-正常
  description?: string
  createdBy?: number
  createdTime: string
  updatedBy?: number
  updatedTime: string
  deleted: number
}

// 文档相关类型
export interface Document {
  id: string
  name: string
  type: string
  size: number
  url: string
  caseId?: string
  contractId?: string
  uploaderId: string
  uploaderName: string
  createdAt: string
}

// 财务相关类型
export interface FinanceRecord {
  id: string
  type: 'income' | 'expense'
  category: string
  amount: number
  description: string
  caseId?: string
  contractId?: string
  date: string
  createdAt: string
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页类型
export interface Pagination {
  current: number
  pageSize: number
  total: number
}

// 表格查询参数
export interface TableQuery {
  page: number
  size: number
  keyword?: string
  [key: string]: any
}
