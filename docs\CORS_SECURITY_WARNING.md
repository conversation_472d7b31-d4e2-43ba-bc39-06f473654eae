# ⚠️ CORS 安全警告

## 🚨 重要提醒

当前配置已设置为**允许所有源访问接口**，这在开发环境中很方便，但在生产环境中存在严重的安全风险。

## 当前配置

### 后端配置
```java
// SecurityConfig.java
configuration.setAllowedOriginPatterns(Collections.singletonList("*"));

// CorsConfig.java  
config.setAllowedOriginPatterns(Collections.singletonList("*"));
```

### 配置文件
```yaml
# application.yml
app:
  cors:
    allowed-origins:
      - "*"
```

## 🔒 生产环境安全配置

在部署到生产环境之前，**必须**修改CORS配置为具体的域名：

### 1. 修改SecurityConfig.java
```java
// 生产环境 - 只允许特定域名
configuration.setAllowedOrigins(Arrays.asList(
    "https://your-frontend-domain.com",
    "https://www.your-frontend-domain.com",
    "https://admin.your-frontend-domain.com"
));
```

### 2. 修改CorsConfig.java
```java
// 生产环境配置
config.setAllowedOrigins(Arrays.asList(
    "https://your-frontend-domain.com",
    "https://www.your-frontend-domain.com"
));
```

### 3. 修改application.yml
```yaml
# 生产环境配置
app:
  cors:
    allowed-origins:
      - https://your-frontend-domain.com
      - https://www.your-frontend-domain.com
```

## 🛡️ 安全最佳实践

### 1. 环境区分配置
```java
@Configuration
@Profile("dev")
public class DevCorsConfig {
    // 开发环境 - 允许所有源
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Collections.singletonList("*"));
        // ... 其他配置
    }
}

@Configuration
@Profile("prod")
public class ProdCorsConfig {
    // 生产环境 - 限制特定域名
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(Arrays.asList(
            "https://your-domain.com"
        ));
        // ... 其他配置
    }
}
```

### 2. 使用配置文件管理
```yaml
# application-dev.yml (开发环境)
app:
  cors:
    allowed-origins:
      - "*"

# application-prod.yml (生产环境)  
app:
  cors:
    allowed-origins:
      - https://your-frontend-domain.com
      - https://www.your-frontend-domain.com
```

### 3. 环境变量配置
```yaml
# application.yml
app:
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:8090}
```

```bash
# 生产环境环境变量
export CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
```

## 🚫 安全风险说明

### 允许所有源的风险：

1. **跨站请求伪造 (CSRF)**
   - 恶意网站可以向您的API发送请求
   - 用户在不知情的情况下执行敏感操作

2. **数据泄露**
   - 任何网站都可以读取API响应数据
   - 敏感信息可能被恶意网站获取

3. **身份验证绕过**
   - 恶意网站可能利用用户的登录状态
   - 执行未授权的操作

## ✅ 部署前检查清单

在部署到生产环境前，请确保：

- [ ] 移除所有 `allowedOriginPatterns("*")` 配置
- [ ] 设置具体的生产域名
- [ ] 启用HTTPS
- [ ] 配置适当的安全头
- [ ] 测试CORS配置是否正常工作
- [ ] 验证只有授权的域名可以访问API

## 🔧 快速修复脚本

创建一个生产环境配置脚本：

```bash
#!/bin/bash
# fix-cors-for-production.sh

echo "修复CORS配置为生产环境..."

# 替换SecurityConfig.java中的配置
sed -i 's/setAllowedOriginPatterns(Collections.singletonList("\*"))/setAllowedOrigins(Arrays.asList("https:\/\/your-domain.com"))/g' backend/src/main/java/com/cloudlegal/config/SecurityConfig.java

# 替换CorsConfig.java中的配置  
sed -i 's/setAllowedOriginPatterns(Collections.singletonList("\*"))/setAllowedOrigins(Arrays.asList("https:\/\/your-domain.com"))/g' backend/src/main/java/com/cloudlegal/config/CorsConfig.java

# 替换application.yml中的配置
sed -i 's/- "\*"/- https:\/\/your-domain.com/g' backend/src/main/resources/application.yml

echo "✅ CORS配置已修复为生产环境安全配置"
echo "⚠️  请将 'your-domain.com' 替换为实际的生产域名"
```

## 📞 联系支持

如果您需要帮助配置生产环境的CORS设置，请联系开发团队。

**记住：安全性永远比便利性更重要！** 🔐
