{"name": "yun<PERSON><PERSON>-frontend", "version": "1.0.0", "description": "云法务系统前端", "scripts": {"dev": "vite", "build": "vite build", "build-with-check": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0", "build": "^0.1.4", "dayjs": "^1.11.9", "echarts": "^5.4.3", "element-plus": "^2.3.8", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "pinia": "^2.1.6", "vue": "^3.3.4", "vue-echarts": "^6.6.0", "vue-router": "^4.2.4", "vue-tsc": "^3.0.4"}, "devDependencies": {"@types/js-cookie": "^3.0.3", "@types/node": "^20.4.5", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "@vitejs/plugin-vue": "^4.2.3", "eslint": "^8.46.0", "eslint-plugin-vue": "^9.15.1", "sass": "^1.64.1", "typescript": "^5.1.6", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.7"}}