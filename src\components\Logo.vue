<template>
  <div class="logo-component" :class="{ 'logo-small': size === 'small', 'logo-large': size === 'large' }">
    <img src="/logo.svg" :alt="alt" />
    <span v-if="showText" class="logo-text">{{ text }}</span>
  </div>
</template>

<script setup lang="ts">
interface Props {
  size?: 'small' | 'medium' | 'large'
  showText?: boolean
  text?: string
  alt?: string
}

withDefaults(defineProps<Props>(), {
  size: 'medium',
  showText: true,
  text: '云法务',
  alt: '云法务系统'
})
</script>

<style lang="scss" scoped>
.logo-component {
  display: flex;
  align-items: center;
  
  img {
    transition: all 0.3s ease;
  }
  
  .logo-text {
    font-weight: 600;
    margin-left: 8px;
    white-space: nowrap;
    transition: all 0.3s ease;
  }
  
  &.logo-small {
    img {
      width: 24px;
      height: 24px;
    }
    
    .logo-text {
      font-size: 14px;
      margin-left: 6px;
    }
  }
  
  &.logo-medium {
    img {
      width: 32px;
      height: 32px;
    }
    
    .logo-text {
      font-size: 16px;
      margin-left: 8px;
    }
  }
  
  &.logo-large {
    img {
      width: 64px;
      height: 64px;
    }
    
    .logo-text {
      font-size: 24px;
      margin-left: 12px;
    }
  }
}
</style>
