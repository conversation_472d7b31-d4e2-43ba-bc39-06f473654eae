package com.cloudlegal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudlegal.common.PageQuery;
import com.cloudlegal.common.PageResult;
import com.cloudlegal.entity.BizClient;

import java.util.List;
import java.util.Map;

/**
 * 客户服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface BizClientService extends IService<BizClient> {

    /**
     * 分页查询客户列表
     */
    PageResult<BizClient> getClientPage(PageQuery pageQuery, Integer clientType, Integer status);

    /**
     * 查询客户列表
     */
    List<BizClient> getClientList(String keyword, Integer clientType, Integer status);

    /**
     * 根据客户名称查询客户
     */
    BizClient getByClientName(String clientName);

    /**
     * 根据统一社会信用代码查询客户
     */
    BizClient getByCreditCode(String creditCode);

    /**
     * 创建客户
     */
    boolean createClient(BizClient client);

    /**
     * 更新客户
     */
    boolean updateClient(BizClient client);

    /**
     * 删除客户
     */
    boolean deleteClient(Long clientId);

    /**
     * 批量删除客户
     */
    boolean batchDeleteClients(List<Long> clientIds);

    /**
     * 更新客户状态
     */
    boolean updateClientStatus(Long clientId, Integer status);

    /**
     * 批量更新客户状态
     */
    boolean batchUpdateClientStatus(List<Long> clientIds, Integer status);

    /**
     * 检查客户名称是否存在
     */
    boolean existsByClientName(String clientName, Long excludeId);

    /**
     * 检查统一社会信用代码是否存在
     */
    boolean existsByCreditCode(String creditCode, Long excludeId);

    /**
     * 获取客户统计信息
     */
    Map<String, Object> getClientStats();

    /**
     * 按客户类型统计数量
     */
    List<Map<String, Object>> getClientStatsByType();

    /**
     * 按行业统计客户数量
     */
    List<Map<String, Object>> getClientStatsByIndustry(Integer limit);

    /**
     * 查询最近创建的客户
     */
    List<BizClient> getRecentClients(Integer limit);
}
