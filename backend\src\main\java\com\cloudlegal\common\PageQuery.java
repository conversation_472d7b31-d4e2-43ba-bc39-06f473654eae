package com.cloudlegal.common;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

/**
 * 分页查询参数
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Schema(description = "分页查询参数")
public class PageQuery {

    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;

    @Schema(description = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能大于100")
    private Integer size = 10;

    @Schema(description = "排序字段", example = "created_time")
    private String sortField;

    @Schema(description = "排序方向", example = "desc")
    private String sortOrder = "desc";

    @Schema(description = "搜索关键词")
    private String keyword;

    public PageQuery() {}

    public PageQuery(Integer page, Integer size) {
        this.page = page;
        this.size = size;
    }

    /**
     * 获取偏移量
     */
    public Integer getOffset() {
        return (page - 1) * size;
    }

    /**
     * 获取限制数量
     */
    public Integer getLimit() {
        return size;
    }

    // Getter and Setter
    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    @Override
    public String toString() {
        return "PageQuery{" +
                "page=" + page +
                ", size=" + size +
                ", sortField='" + sortField + '\'' +
                ", sortOrder='" + sortOrder + '\'' +
                ", keyword='" + keyword + '\'' +
                '}';
    }
}
