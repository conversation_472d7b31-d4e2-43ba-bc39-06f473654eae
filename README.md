# 云法务系统前端

一套完整的云法务系统PC端前端页面，基于Vue3 + TypeScript + Element Plus构建。

## 功能特性

### 核心功能模块

- **用户认证和权限管理**
  - 登录/登出
  - 用户管理
  - 角色权限控制

- **案件管理**
  - 案件列表和详情
  - 案件创建/编辑
  - 案件状态跟踪
  - 案件进展记录

- **合同管理**
  - 合同列表和详情
  - 合同模板管理
  - 合同审批流程
  - 合同状态管理

- **文档管理**
  - 文档上传/下载
  - 文档分类管理
  - 在线预览
  - 版本控制

- **客户管理**
  - 客户信息管理
  - 客户关系维护
  - 客户案件关联

- **财务管理**
  - 费用管理
  - 收支统计
  - 财务报表
  - 数据图表

- **工作台和数据看板**
  - 个人工作台
  - 数据统计图表
  - 待办事项
  - 系统通知

- **系统设置**
  - 个人设置
  - 系统配置
  - 组织架构管理

### 技术特性

- **现代化技术栈**
  - Vue 3 + Composition API
  - TypeScript 支持
  - Element Plus UI组件库
  - Pinia 状态管理
  - Vue Router 路由管理

- **开发体验**
  - Vite 构建工具
  - 热重载开发
  - ESLint 代码规范
  - 自动导入配置

- **UI/UX设计**
  - 响应式布局
  - 现代化界面设计
  - 丰富的交互效果
  - 完善的错误处理

## 项目结构

```
src/
├── api/                 # API接口
├── components/          # 通用组件
├── layout/             # 布局组件
├── router/             # 路由配置
├── stores/             # 状态管理
├── styles/             # 样式文件
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
├── views/              # 页面组件
│   ├── login/          # 登录页面
│   ├── dashboard/      # 工作台
│   ├── cases/          # 案件管理
│   ├── contracts/      # 合同管理
│   ├── clients/        # 客户管理
│   ├── documents/      # 文档管理
│   ├── finance/        # 财务管理
│   ├── users/          # 用户管理
│   ├── settings/       # 系统设置
│   └── error/          # 错误页面
├── App.vue             # 根组件
└── main.ts             # 入口文件
```

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 7.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发环境

```bash
npm run dev
# 或
yarn dev
```

访问 http://localhost:3000

### 演示账号

系统内置了以下演示账号，您可以直接使用：

| 角色 | 用户名 | 密码 | 权限说明 |
|------|--------|------|----------|
| 系统管理员 | `admin` | `123456` | 拥有所有功能权限 |
| 律师 | `lawyer` | `123456` | 案件、合同、客户、文档管理权限 |
| 助理 | `assistant` | `123456` | 客户、文档管理权限 |
| 普通用户 | `user` | `123456` | 只读权限 |

**推荐使用管理员账号 `admin/123456` 体验完整功能。**

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

### 代码检查

```bash
npm run lint
# 或
yarn lint
```

### 类型检查

```bash
npm run type-check
# 或
yarn type-check
```

## 配置说明

### 环境变量

创建 `.env.local` 文件配置本地环境变量：

```env
# API基础URL
VITE_API_BASE_URL=http://localhost:8080

# 应用标题
VITE_APP_TITLE=云法务系统
```

### 代理配置

在 `vite.config.ts` 中配置API代理：

```typescript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '')
    }
  }
}
```

## 部署说明

### 静态部署

构建完成后，将 `dist` 目录部署到静态服务器即可。

### Nginx配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://backend-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 开发指南

### 添加新页面

1. 在 `src/views/` 下创建页面组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 在侧边栏菜单中添加导航项

### 添加新API

1. 在 `src/api/` 下创建API模块
2. 在 `src/types/` 中定义相关类型
3. 在组件中调用API接口

### 状态管理

使用Pinia进行状态管理，在 `src/stores/` 下创建store模块。

### 样式规范

- 使用SCSS预处理器
- 遵循BEM命名规范
- 使用CSS变量定义主题色彩

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 联系方式

如有问题或建议，请联系开发团队。
