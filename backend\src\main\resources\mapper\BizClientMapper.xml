<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudlegal.mapper.BizClientMapper">

    <!-- 分页查询客户列表 -->
    <select id="selectClientPage" resultType="com.cloudlegal.entity.BizClient">
        SELECT *
        FROM biz_client
        WHERE deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (client_name LIKE CONCAT('%', #{keyword}, '%')
            OR contact_person LIKE CONCAT('%', #{keyword}, '%')
            OR phone LIKE CONCAT('%', #{keyword}, '%')
            OR email LIKE CONCAT('%', #{keyword}, '%')
            OR credit_code LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="clientType != null">
            AND client_type = #{clientType}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 查询客户列表 -->
    <select id="selectClientList" resultType="com.cloudlegal.entity.BizClient">
        SELECT *
        FROM biz_client
        WHERE deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (client_name LIKE CONCAT('%', #{keyword}, '%')
            OR contact_person LIKE CONCAT('%', #{keyword}, '%')
            OR phone LIKE CONCAT('%', #{keyword}, '%')
            OR email LIKE CONCAT('%', #{keyword}, '%')
            OR credit_code LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="clientType != null">
            AND client_type = #{clientType}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据客户名称查询客户 -->
    <select id="selectByClientName" resultType="com.cloudlegal.entity.BizClient">
        SELECT *
        FROM biz_client
        WHERE client_name = #{clientName}
        AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据统一社会信用代码查询客户 -->
    <select id="selectByCreditCode" resultType="com.cloudlegal.entity.BizClient">
        SELECT *
        FROM biz_client
        WHERE credit_code = #{creditCode}
        AND deleted = 0
        LIMIT 1
    </select>

    <!-- 统计客户数量 -->
    <select id="countClients" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM biz_client
        WHERE deleted = 0
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

    <!-- 按客户类型统计数量 -->
    <select id="countByClientType" resultType="java.util.Map">
        SELECT 
            client_type,
            CASE 
                WHEN client_type = 1 THEN '个人'
                WHEN client_type = 2 THEN '企业'
                WHEN client_type = 3 THEN '组织'
                ELSE '其他'
            END as type_name,
            COUNT(*) as count
        FROM biz_client
        WHERE deleted = 0
        GROUP BY client_type
        ORDER BY client_type
    </select>

    <!-- 按行业统计客户数量 -->
    <select id="countByIndustry" resultType="java.util.Map">
        SELECT 
            COALESCE(industry, '未分类') as industry,
            COUNT(*) as count
        FROM biz_client
        WHERE deleted = 0
        AND client_type = 2
        GROUP BY industry
        ORDER BY count DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询最近创建的客户 -->
    <select id="selectRecentClients" resultType="com.cloudlegal.entity.BizClient">
        SELECT *
        FROM biz_client
        WHERE deleted = 0
        ORDER BY created_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量更新客户状态 -->
    <update id="batchUpdateStatus">
        UPDATE biz_client 
        SET status = #{status}
        WHERE id IN
        <foreach collection="clientIds" item="clientId" open="(" separator="," close=")">
            #{clientId}
        </foreach>
    </update>

    <!-- 查询客户统计信息 -->
    <select id="selectClientStats" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactive,
            SUM(CASE WHEN client_type = 1 THEN 1 ELSE 0 END) as individual_count,
            SUM(CASE WHEN client_type = 2 THEN 1 ELSE 0 END) as corporate_count,
            SUM(CASE WHEN client_type = 3 THEN 1 ELSE 0 END) as organization_count,
            SUM(CASE WHEN DATE(created_time) = CURDATE() THEN 1 ELSE 0 END) as today_new,
            SUM(CASE WHEN DATE(created_time) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as week_new,
            SUM(CASE WHEN DATE(created_time) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as month_new
        FROM biz_client
        WHERE deleted = 0
    </select>

</mapper>
