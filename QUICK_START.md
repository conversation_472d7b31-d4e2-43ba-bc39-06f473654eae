# 🚀 云法务系统 - 快速启动指南

## 📋 系统要求

- Node.js >= 16.0.0
- npm >= 7.0.0 或 yarn >= 1.22.0

## ⚡ 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

### 3. 访问系统

打开浏览器访问：http://localhost:3000

## 🔐 登录系统

系统提供了以下演示账号，点击即可快速填充：

### 🔑 演示账号

| 角色 | 用户名 | 密码 | 功能权限 |
|------|--------|------|----------|
| **系统管理员** | `admin` | `123456` | ✅ 所有功能 |
| **律师** | `lawyer` | `123456` | ✅ 案件、合同、客户、文档管理 |
| **助理** | `assistant` | `123456` | ✅ 客户、文档管理 |
| **普通用户** | `user` | `123456` | ✅ 只读权限 |

### 💡 推荐体验流程

1. **使用管理员账号登录** (`admin/123456`)
2. **浏览工作台** - 查看数据统计和快捷操作
3. **体验案件管理** - 创建、编辑、查看案件详情
4. **体验合同管理** - 管理合同和模板
5. **体验客户管理** - 添加和管理客户信息
6. **体验文档管理** - 上传和预览文档
7. **体验财务管理** - 查看收支统计和图表
8. **体验用户管理** - 管理系统用户
9. **体验系统设置** - 个人设置和系统配置

## 🎯 核心功能导航

### 📊 工作台 (`/dashboard`)
- 数据统计卡片
- 案件趋势图表
- 待办事项管理
- 快捷操作入口

### 📁 案件管理 (`/cases`)
- 案件列表和搜索
- 案件详情和进展跟踪
- 案件状态管理
- 相关文档和合同

### 📄 合同管理 (`/contracts`)
- 合同列表和状态
- 合同模板管理
- 合同详情和条款
- 审批流程记录

### 👥 客户管理 (`/clients`)
- 客户信息管理
- 客户详情查看
- 相关案件和合同
- 客户关系维护

### 📎 文档管理 (`/documents`)
- 文档上传和下载
- 文档分类管理
- 在线预览功能
- 文档关联管理

### 💰 财务管理 (`/finance`)
- 收支记录管理
- 财务统计图表
- 数据可视化
- 财务报表导出

### 👤 用户管理 (`/users`)
- 用户账号管理
- 角色权限分配
- 用户状态控制
- 密码重置功能

### ⚙️ 系统设置 (`/settings`)
- 个人信息设置
- 安全设置
- 通知设置
- 组织架构管理

## 🎨 界面特性

- **响应式设计** - 支持桌面、平板、手机
- **现代化UI** - 基于Element Plus组件库
- **深色模式** - 支持主题切换
- **国际化** - 多语言支持准备
- **无障碍** - 良好的可访问性

## 🛠 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **构建工具**: Vite
- **样式处理**: SCSS
- **图表库**: ECharts
- **HTTP客户端**: Axios

## 📱 移动端体验

系统完全支持移动端访问：

1. 在手机浏览器中打开 http://localhost:3000
2. 使用相同的演示账号登录
3. 体验移动端优化的界面和交互

## 🔧 开发模式

当前系统运行在**Mock模式**下，所有数据都是模拟数据，包括：

- ✅ 用户登录认证
- ✅ 数据增删改查
- ✅ 文件上传下载
- ✅ 图表数据展示

## 📞 技术支持

如果在使用过程中遇到问题：

1. 检查Node.js版本是否符合要求
2. 确保端口3000未被占用
3. 清除浏览器缓存后重试
4. 查看控制台错误信息

## 🎉 开始体验

现在您可以开始体验云法务系统的完整功能了！

**建议从管理员账号开始：`admin/123456`**
