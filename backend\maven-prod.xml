<?xml version="1.0" encoding="UTF-8"?>
<!-- 生产环境Maven配置 - 排除测试文件 -->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    
    <!-- 继承主POM配置 -->
    <parent>
        <groupId>com.cloudlegal</groupId>
        <artifactId>cloud-legal-backend</artifactId>
        <version>1.0.0</version>
        <relativePath>./pom.xml</relativePath>
    </parent>
    
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cloud-legal-backend-prod</artifactId>
    <packaging>jar</packaging>
    <name>cloud-legal-backend-prod</name>
    <description>云法务系统后端服务 - 生产环境版本</description>
    
    <properties>
        <!-- 强制跳过测试 -->
        <maven.test.skip>true</maven.test.skip>
        <skipTests>true</skipTests>
        <maven.javadoc.skip>true</maven.javadoc.skip>
    </properties>
    
    <build>
        <!-- 排除测试源码目录 -->
        <testSourceDirectory>src/test/java</testSourceDirectory>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <excludes>
                    <exclude>**/*</exclude>
                </excludes>
            </testResource>
        </testResources>
        
        <plugins>
            <!-- Spring Boot Maven Plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <!-- 排除测试相关依赖 -->
                        <exclude>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-starter-test</artifactId>
                        </exclude>
                        <exclude>
                            <groupId>org.springframework.security</groupId>
                            <artifactId>spring-security-test</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            
            <!-- Maven Compiler Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <encoding>UTF-8</encoding>
                    <!-- 排除测试文件编译 -->
                    <testExcludes>
                        <testExclude>**/*Test.java</testExclude>
                        <testExclude>**/*Tests.java</testExclude>
                        <testExclude>**/*TestCase.java</testExclude>
                    </testExcludes>
                </configuration>
            </plugin>
            
            <!-- Maven Surefire Plugin - 完全跳过测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                    <skip>true</skip>
                </configuration>
            </plugin>
            
            <!-- Maven Failsafe Plugin - 跳过集成测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                    <skip>true</skip>
                </configuration>
            </plugin>
            
            <!-- Maven JAR Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <!-- 排除测试类 -->
                        <exclude>**/*Test.class</exclude>
                        <exclude>**/*Tests.class</exclude>
                        <exclude>**/*TestCase.class</exclude>
                        <exclude>**/test/**</exclude>
                    </excludes>
                </configuration>
            </plugin>
            
            <!-- Maven Resources Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.outputDirectory}</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/resources</directory>
                                    <excludes>
                                        <!-- 排除测试配置文件 -->
                                        <exclude>**/*.test.properties</exclude>
                                        <exclude>**/*.test.yml</exclude>
                                        <exclude>**/*.test.yaml</exclude>
                                        <exclude>**/test/**</exclude>
                                    </excludes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
