/**
 * CORS问题诊断脚本
 * 用于诊断和解决跨域问题
 */

const axios = require('axios')
const colors = require('colors')

// 配置
const config = {
  frontend: 'http://localhost:8090',
  backend: 'http://localhost:8080',
  apiPath: '/api',
  testEndpoints: [
    '/test/cors/health',
    '/test/cors/get',
    '/test/cors/headers',
    '/auth/login'
  ]
}

console.log('🔍 CORS问题诊断工具'.cyan.bold)
console.log('=' .repeat(50))

async function checkService(url, name) {
  try {
    const response = await axios.get(url, { timeout: 3000 })
    console.log(`✅ ${name}服务正常运行`.green)
    return true
  } catch (error) {
    console.log(`❌ ${name}服务异常: ${error.message}`.red)
    return false
  }
}

async function testCorsRequest(url, method = 'GET', data = null) {
  try {
    const config = {
      method,
      url,
      timeout: 5000,
      headers: {
        'Origin': 'http://localhost:8090',
        'Content-Type': 'application/json'
      }
    }
    
    if (data && method !== 'GET') {
      config.data = data
    }
    
    const response = await axios(config)
    return {
      success: true,
      status: response.status,
      headers: response.headers,
      data: response.data
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status,
      headers: error.response?.headers,
      data: error.response?.data
    }
  }
}

async function testPreflightRequest(url) {
  try {
    const response = await axios.options(url, {
      headers: {
        'Origin': 'http://localhost:8090',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type,Authorization'
      },
      timeout: 5000
    })
    
    return {
      success: true,
      status: response.status,
      headers: response.headers,
      allowedOrigins: response.headers['access-control-allow-origin'],
      allowedMethods: response.headers['access-control-allow-methods'],
      allowedHeaders: response.headers['access-control-allow-headers'],
      allowCredentials: response.headers['access-control-allow-credentials']
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status
    }
  }
}

async function diagnose() {
  console.log('\n1. 检查服务状态'.yellow.bold)
  console.log('-'.repeat(30))
  
  const frontendOk = await checkService(config.frontend, '前端')
  const backendOk = await checkService(`${config.backend}${config.apiPath}/test/cors/health`, '后端')
  
  if (!frontendOk || !backendOk) {
    console.log('\n⚠️  请确保前后端服务都已启动'.yellow)
    return
  }
  
  console.log('\n2. 测试CORS配置'.yellow.bold)
  console.log('-'.repeat(30))
  
  // 测试预检请求
  console.log('\n📋 测试OPTIONS预检请求:')
  const preflightResult = await testPreflightRequest(`${config.backend}${config.apiPath}/test/cors/post`)
  
  if (preflightResult.success) {
    console.log('✅ OPTIONS请求成功'.green)
    console.log(`   允许的源: ${preflightResult.allowedOrigins || '未设置'}`)
    console.log(`   允许的方法: ${preflightResult.allowedMethods || '未设置'}`)
    console.log(`   允许的头: ${preflightResult.allowedHeaders || '未设置'}`)
    console.log(`   允许凭证: ${preflightResult.allowCredentials || '未设置'}`)
  } else {
    console.log(`❌ OPTIONS请求失败: ${preflightResult.error}`.red)
  }
  
  // 测试实际请求
  console.log('\n📋 测试实际请求:')
  for (const endpoint of config.testEndpoints) {
    const url = `${config.backend}${config.apiPath}${endpoint}`
    console.log(`\n测试: ${endpoint}`)
    
    const result = await testCorsRequest(url)
    if (result.success) {
      console.log(`✅ GET请求成功 (${result.status})`.green)
      
      // 检查CORS头
      const corsHeaders = {
        'access-control-allow-origin': result.headers['access-control-allow-origin'],
        'access-control-allow-credentials': result.headers['access-control-allow-credentials'],
        'access-control-expose-headers': result.headers['access-control-expose-headers']
      }
      
      console.log('   CORS响应头:')
      Object.entries(corsHeaders).forEach(([key, value]) => {
        if (value) {
          console.log(`     ${key}: ${value}`)
        }
      })
    } else {
      console.log(`❌ GET请求失败: ${result.error}`.red)
      if (result.status) {
        console.log(`   状态码: ${result.status}`)
      }
    }
  }
  
  console.log('\n3. 诊断建议'.yellow.bold)
  console.log('-'.repeat(30))
  
  // 基于测试结果给出建议
  if (!preflightResult.success) {
    console.log('❌ OPTIONS预检请求失败，建议检查:'.red)
    console.log('   1. 后端CORS配置是否正确')
    console.log('   2. SecurityConfig中的CORS配置')
    console.log('   3. 是否有多个CORS配置冲突')
  }
  
  if (preflightResult.allowedOrigins !== '*' && !preflightResult.allowedOrigins?.includes('localhost:8090')) {
    console.log('⚠️  允许的源可能不包含前端地址'.yellow)
    console.log('   建议在后端CORS配置中添加: http://localhost:8090')
  }
  
  console.log('\n✅ 诊断完成！'.green.bold)
  console.log('\n如果问题仍然存在，请检查:')
  console.log('1. 浏览器开发者工具的Network面板')
  console.log('2. 后端服务日志')
  console.log('3. 前端代理配置 (vite.config.ts)')
  console.log('4. 后端CORS配置 (SecurityConfig.java)')
}

// 运行诊断
diagnose().catch(console.error)
