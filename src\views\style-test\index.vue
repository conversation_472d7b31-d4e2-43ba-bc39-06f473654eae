<template>
  <div class="style-test-page">
    <div class="card">
      <h2>导航栏样式测试</h2>
      <p>请点击左侧导航栏的不同菜单项，观察背景样式是否保持一致。</p>
      
      <div class="test-info">
        <h3>当前页面信息</h3>
        <ul>
          <li><strong>当前路由:</strong> {{ $route.path }}</li>
          <li><strong>页面标题:</strong> {{ $route.meta?.title }}</li>
          <li><strong>访问时间:</strong> {{ currentTime }}</li>
        </ul>
      </div>
      
      <div class="menu-test">
        <h3>菜单项测试</h3>
        <p>以下是所有可用的菜单项，点击测试导航:</p>
        <div class="menu-grid">
          <router-link 
            v-for="route in menuRoutes" 
            :key="route.path"
            :to="route.path"
            class="menu-test-item"
            :class="{ active: $route.path === route.path }"
          >
            <el-icon v-if="route.meta?.icon">
              <component :is="route.meta.icon" />
            </el-icon>
            <span>{{ route.meta?.title }}</span>
          </router-link>
        </div>
      </div>
      
      <div class="style-checklist">
        <h3>样式检查清单</h3>
        <el-checkbox-group v-model="checkedItems">
          <div class="checklist-item">
            <el-checkbox label="width">所有菜单项宽度一致</el-checkbox>
          </div>
          <div class="checklist-item">
            <el-checkbox label="height">所有菜单项高度一致</el-checkbox>
          </div>
          <div class="checklist-item">
            <el-checkbox label="padding">所有菜单项内边距一致</el-checkbox>
          </div>
          <div class="checklist-item">
            <el-checkbox label="active">激活状态样式正确</el-checkbox>
          </div>
          <div class="checklist-item">
            <el-checkbox label="hover">悬停状态样式正确</el-checkbox>
          </div>
          <div class="checklist-item">
            <el-checkbox label="collapse">折叠状态样式正确</el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
      
      <div class="test-actions">
        <el-button @click="toggleSidebar">切换侧边栏折叠</el-button>
        <el-button @click="checkAllItems">全部检查</el-button>
        <el-button @click="clearChecks">清除检查</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const currentTime = ref('')
const checkedItems = ref<string[]>([])

// 获取菜单路由
const menuRoutes = computed(() => {
  const mainRoute = router.getRoutes().find(r => r.path === '/')
  if (!mainRoute?.children) return []
  
  return mainRoute.children
    .filter(child => child.meta?.title && !child.meta?.hidden)
    .map(child => ({
      ...child,
      path: child.path.startsWith('/') ? child.path : '/' + child.path
    }))
})

// 更新时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

// 切换侧边栏
const toggleSidebar = () => {
  // 这里需要调用父组件的方法，暂时用事件模拟
  window.dispatchEvent(new CustomEvent('toggle-sidebar'))
}

// 全部检查
const checkAllItems = () => {
  checkedItems.value = ['width', 'height', 'padding', 'active', 'hover', 'collapse']
}

// 清除检查
const clearChecks = () => {
  checkedItems.value = []
}

let timer: NodeJS.Timeout

onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style lang="scss" scoped>
.style-test-page {
  padding: 20px;
  
  .card {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  h2 {
    color: #303133;
    margin-bottom: 16px;
  }
  
  h3 {
    color: #606266;
    margin: 24px 0 16px 0;
    font-size: 16px;
  }
  
  .test-info {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
    margin: 20px 0;
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin: 8px 0;
        color: #606266;
      }
    }
  }
  
  .menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin: 16px 0;
    
    .menu-test-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      background: #f0f9ff;
      border: 1px solid #e1f5fe;
      border-radius: 6px;
      text-decoration: none;
      color: #409eff;
      transition: all 0.3s;
      
      &:hover {
        background: #e3f2fd;
        border-color: #409eff;
        transform: translateY(-1px);
      }
      
      &.active {
        background: #409eff;
        color: #fff;
        border-color: #409eff;
      }
      
      .el-icon {
        margin-right: 8px;
        font-size: 16px;
      }
      
      span {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
  
  .style-checklist {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 6px;
    margin: 20px 0;
    
    .checklist-item {
      margin: 12px 0;
      
      .el-checkbox {
        width: 100%;
        
        :deep(.el-checkbox__label) {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }
  
  .test-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
    
    .el-button {
      flex: 1;
    }
  }
}

@media (max-width: 768px) {
  .style-test-page {
    padding: 10px;
    
    .menu-grid {
      grid-template-columns: 1fr;
    }
    
    .test-actions {
      flex-direction: column;
      
      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
