package com.cloudlegal.controller;

import com.cloudlegal.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * CORS测试控制器
 * 用于测试跨域配置是否正确
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Tag(name = "CORS测试", description = "跨域配置测试接口")
@RestController
@RequestMapping("/test/cors")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CorsTestController {

    @Operation(summary = "CORS GET测试", description = "测试GET请求的跨域配置")
    @GetMapping("/get")
    public Result<Map<String, Object>> testGet(HttpServletRequest request) {
        Map<String, Object> data = new HashMap<>();
        data.put("method", "GET");
        data.put("origin", request.getHeader("Origin"));
        data.put("referer", request.getHeader("Referer"));
        data.put("userAgent", request.getHeader("User-Agent"));
        data.put("timestamp", System.currentTimeMillis());
        data.put("message", "CORS GET请求测试成功");
        
        return Result.success("GET请求成功", data);
    }

    @Operation(summary = "CORS POST测试", description = "测试POST请求的跨域配置")
    @PostMapping("/post")
    public Result<Map<String, Object>> testPost(@RequestBody(required = false) Map<String, Object> requestData,
                                                HttpServletRequest request) {
        Map<String, Object> data = new HashMap<>();
        data.put("method", "POST");
        data.put("origin", request.getHeader("Origin"));
        data.put("referer", request.getHeader("Referer"));
        data.put("contentType", request.getHeader("Content-Type"));
        data.put("requestData", requestData);
        data.put("timestamp", System.currentTimeMillis());
        data.put("message", "CORS POST请求测试成功");
        
        return Result.success("POST请求成功", data);
    }

    @Operation(summary = "CORS OPTIONS测试", description = "测试OPTIONS预检请求")
    @RequestMapping(value = "/options", method = RequestMethod.OPTIONS)
    public Result<Map<String, Object>> testOptions(HttpServletRequest request) {
        Map<String, Object> data = new HashMap<>();
        data.put("method", "OPTIONS");
        data.put("origin", request.getHeader("Origin"));
        data.put("accessControlRequestMethod", request.getHeader("Access-Control-Request-Method"));
        data.put("accessControlRequestHeaders", request.getHeader("Access-Control-Request-Headers"));
        data.put("timestamp", System.currentTimeMillis());
        data.put("message", "CORS OPTIONS预检请求测试成功");
        
        return Result.success("OPTIONS请求成功", data);
    }

    @Operation(summary = "获取请求头信息", description = "获取所有请求头信息用于调试")
    @GetMapping("/headers")
    public Result<Map<String, Object>> getHeaders(HttpServletRequest request) {
        Map<String, Object> headers = new HashMap<>();
        
        request.getHeaderNames().asIterator().forEachRemaining(headerName -> {
            headers.put(headerName, request.getHeader(headerName));
        });
        
        Map<String, Object> data = new HashMap<>();
        data.put("headers", headers);
        data.put("method", request.getMethod());
        data.put("requestURL", request.getRequestURL().toString());
        data.put("queryString", request.getQueryString());
        data.put("remoteAddr", request.getRemoteAddr());
        data.put("timestamp", System.currentTimeMillis());
        
        return Result.success("获取请求头成功", data);
    }

    @Operation(summary = "健康检查", description = "简单的健康检查接口")
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("服务正常运行", "OK");
    }
}
