# 云法务系统后端服务

## 项目简介

云法务系统后端服务是一个基于Spring Boot 3.x开发的企业级法务管理系统，提供完整的法务业务管理功能，包括客户管理、案件管理、合同管理、文档管理、财务管理等核心功能。

## 技术栈

- **框架**: Spring Boot 3.2.0
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis Plus 3.5.4
- **安全**: Spring Security 6.x
- **认证**: JWT (JSON Web Token)
- **缓存**: Redis 7.x
- **文档**: Knife4j (Swagger 3)
- **文件存储**: MinIO
- **构建工具**: Maven 3.8+
- **JDK版本**: Java 17+

## 功能特性

### 🔐 认证授权
- JWT Token认证
- 基于角色的权限控制(RBAC)
- 用户登录/登出
- Token刷新机制

### 👥 用户管理
- 用户增删改查
- 角色权限管理
- 用户状态管理
- 密码加密存储

### 🏢 客户管理
- 客户信息管理
- 企业/个人/组织分类
- 客户状态跟踪
- 客户统计分析

### ⚖️ 案件管理
- 案件全生命周期管理
- 案件类型分类
- 优先级管理
- 案件状态跟踪

### 📄 合同管理
- 合同信息管理
- 合同状态跟踪
- 合同类型分类
- 合同到期提醒

### 📁 文档管理
- 文件上传下载
- 文档分类管理
- 文档关联业务
- 文件格式支持

### 💰 财务管理
- 收支记录管理
- 财务统计分析
- 业务关联记录
- 财务报表生成

## 快速开始

### 环境要求

- JDK 17+
- Maven 3.8+
- MySQL 8.0+
- Redis 7.x
- MinIO (可选)

### 1. 克隆项目

```bash
git clone <repository-url>
cd cloud-legal-backend
```

### 2. 数据库配置

1. 创建MySQL数据库：
```sql
CREATE DATABASE cloud_legal DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行初始化脚本：
```bash
mysql -u root -p cloud_legal < sql/cloud_legal.sql
```

### 3. 配置文件

修改 `src/main/resources/application.yml` 中的配置：

```yaml
spring:
  datasource:
    url: ****************************************************************************************************************************************************
    username: your_username
    password: your_password
  
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
```

### 4. 启动服务

```bash
# 使用Maven启动
mvn spring-boot:run

# 或者打包后启动
mvn clean package
java -jar target/cloud-legal-backend-1.0.0.jar
```

### 5. 访问接口文档

启动成功后，访问：http://localhost:8080/api/doc.html

## 项目结构

```
src/main/java/com/cloudlegal/
├── CloudLegalApplication.java          # 启动类
├── common/                             # 通用类
│   ├── Result.java                     # 统一响应结果
│   ├── ResultCode.java                 # 响应状态码
│   ├── PageQuery.java                  # 分页查询参数
│   └── PageResult.java                 # 分页结果
├── config/                             # 配置类
│   ├── SecurityConfig.java             # 安全配置
│   ├── MybatisPlusConfig.java          # MyBatis Plus配置
│   └── SwaggerConfig.java              # Swagger配置
├── controller/                         # 控制器
│   ├── AuthController.java             # 认证控制器
│   ├── BizClientController.java        # 客户管理控制器
│   ├── BizCaseController.java          # 案件管理控制器
│   └── ...
├── dto/                                # 数据传输对象
│   ├── LoginRequest.java               # 登录请求
│   ├── LoginResponse.java              # 登录响应
│   └── ...
├── entity/                             # 实体类
│   ├── BaseEntity.java                 # 基础实体
│   ├── SysUser.java                    # 用户实体
│   ├── BizClient.java                  # 客户实体
│   └── ...
├── mapper/                             # Mapper接口
│   ├── SysUserMapper.java              # 用户Mapper
│   ├── BizClientMapper.java            # 客户Mapper
│   └── ...
├── service/                            # 服务接口
│   ├── AuthService.java                # 认证服务
│   ├── SysUserService.java             # 用户服务
│   ├── BizClientService.java           # 客户服务
│   └── impl/                           # 服务实现
└── utils/                              # 工具类
    ├── JwtUtils.java                   # JWT工具类
    ├── PasswordUtils.java              # 密码工具类
    └── ...
```

## API接口

### 认证相关
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - 刷新Token
- `GET /api/auth/me` - 获取当前用户信息

### 客户管理
- `GET /api/client/page` - 分页查询客户
- `GET /api/client/list` - 查询客户列表
- `GET /api/client/{id}` - 查询客户详情
- `POST /api/client` - 创建客户
- `PUT /api/client/{id}` - 更新客户
- `DELETE /api/client/{id}` - 删除客户
- `GET /api/client/stats` - 客户统计

### 案件管理
- `GET /api/case/page` - 分页查询案件
- `GET /api/case/list` - 查询案件列表
- `GET /api/case/{id}` - 查询案件详情
- `POST /api/case` - 创建案件
- `PUT /api/case/{id}` - 更新案件
- `DELETE /api/case/{id}` - 删除案件
- `GET /api/case/stats` - 案件统计

## 默认账号

系统初始化后会创建以下默认账号：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | 123456 | 超级管理员 | 系统管理员账号 |
| lawyer1 | 123456 | 律师 | 张律师账号 |
| assistant1 | 123456 | 助理 | 李助理账号 |

## 开发指南

### 添加新功能模块

1. 创建实体类继承 `BaseEntity`
2. 创建Mapper接口继承 `BaseMapper`
3. 创建Service接口继承 `IService`
4. 创建Service实现类继承 `ServiceImpl`
5. 创建Controller类添加REST接口
6. 编写Mapper XML文件（如需要）

### 代码规范

- 使用统一的代码格式化配置
- 遵循阿里巴巴Java开发手册
- 接口需要添加Swagger注解
- 重要方法需要添加注释
- 异常处理使用统一的异常处理器

### 数据库规范

- 表名使用下划线命名法
- 字段名使用下划线命名法
- 必须包含创建时间、更新时间字段
- 软删除使用deleted字段
- 主键统一使用雪花算法生成

## 部署说明

### Docker部署

```bash
# 构建镜像
docker build -t cloud-legal-backend .

# 运行容器
docker run -d \
  --name cloud-legal-backend \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  cloud-legal-backend
```

### 生产环境配置

1. 修改数据库连接配置
2. 配置Redis连接信息
3. 设置JWT密钥
4. 配置文件存储路径
5. 设置日志级别和路径

## 常见问题

### Q: 启动时数据库连接失败？
A: 检查数据库配置信息，确保数据库服务正常运行，用户名密码正确。

### Q: Redis连接失败？
A: 检查Redis服务是否启动，连接信息是否正确。

### Q: 接口返回401未授权？
A: 检查Token是否正确，是否已过期，请求头是否包含Authorization。

### Q: 文件上传失败？
A: 检查文件大小是否超出限制，文件类型是否支持，存储路径是否有写权限。

## 联系方式

- 项目地址：[GitHub Repository]
- 技术支持：<EMAIL>
- 文档地址：[Documentation URL]

## 许可证

本项目采用 MIT 许可证，详情请参阅 [LICENSE](LICENSE) 文件。
