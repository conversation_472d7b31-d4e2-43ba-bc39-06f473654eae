<template>
  <div class="cors-test">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>CORS 跨域测试</span>
          <el-button type="primary" @click="runAllTests">运行所有测试</el-button>
        </div>
      </template>

      <!-- 配置信息 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="8">
          <el-card>
            <h4>前端配置</h4>
            <p><strong>地址:</strong> {{ frontendUrl }}</p>
            <p><strong>端口:</strong> 8090</p>
            <p><strong>API Base:</strong> {{ apiBaseUrl }}</p>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <h4>后端配置</h4>
            <p><strong>地址:</strong> {{ backendUrl }}</p>
            <p><strong>端口:</strong> 8080</p>
            <p><strong>Context Path:</strong> /api</p>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card>
            <h4>代理配置</h4>
            <p><strong>代理:</strong> /api → http://localhost:8080</p>
            <p><strong>Change Origin:</strong> true</p>
            <p><strong>Secure:</strong> false</p>
          </el-card>
        </el-col>
      </el-row>

      <!-- 测试按钮 -->
      <el-row :gutter="20" class="mb-4">
        <el-col :span="6">
          <el-button 
            type="success" 
            @click="testGet" 
            :loading="testing.get"
            style="width: 100%"
          >
            测试 GET 请求
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button 
            type="warning" 
            @click="testPost" 
            :loading="testing.post"
            style="width: 100%"
          >
            测试 POST 请求
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button 
            type="info" 
            @click="testOptions" 
            :loading="testing.options"
            style="width: 100%"
          >
            测试 OPTIONS 请求
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button 
            type="primary" 
            @click="testHeaders" 
            :loading="testing.headers"
            style="width: 100%"
          >
            获取请求头
          </el-button>
        </el-col>
      </el-row>

      <!-- 测试结果 -->
      <el-card v-if="testResults.length > 0">
        <template #header>
          <div class="card-header">
            <span>测试结果</span>
            <el-button type="danger" size="small" @click="clearResults">清空结果</el-button>
          </div>
        </template>
        
        <div class="test-results">
          <div 
            v-for="result in testResults" 
            :key="result.id"
            class="test-result-item"
            :class="`result-${result.type}`"
          >
            <div class="result-header">
              <el-tag :type="result.type">{{ result.title }}</el-tag>
              <span class="timestamp">{{ result.timestamp }}</span>
            </div>
            <div class="result-message">{{ result.message }}</div>
            <div v-if="result.data" class="result-data">
              <el-collapse>
                <el-collapse-item title="详细数据" name="1">
                  <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'

// 响应式数据
const frontendUrl = ref(window.location.origin)
const apiBaseUrl = ref(import.meta.env.VITE_API_BASE_URL || '/api')
const backendUrl = ref('http://localhost:8080')

const testing = ref({
  get: false,
  post: false,
  options: false,
  headers: false
})

const testResults = ref<Array<{
  id: number
  title: string
  message: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  timestamp: string
  data?: any
}>>([])

let resultId = 0

// 添加测试结果
const addResult = (title: string, message: string, type: string, data?: any) => {
  testResults.value.unshift({
    id: ++resultId,
    title,
    message,
    type: type as any,
    timestamp: new Date().toLocaleTimeString(),
    data
  })
}

// 清空结果
const clearResults = () => {
  testResults.value = []
}

// 测试GET请求
const testGet = async () => {
  testing.value.get = true
  try {
    const response = await axios.get('/api/test/cors/get')
    addResult('GET 请求', '成功', 'success', response)
    ElMessage.success('GET请求测试成功')
  } catch (error: any) {
    addResult('GET 请求', `失败: ${error.message}`, 'danger', error.response?.data)
    ElMessage.error(`GET请求测试失败: ${error.message}`)
  } finally {
    testing.value.get = false
  }
}

// 测试POST请求
const testPost = async () => {
  testing.value.post = true
  try {
    const testData = {
      message: 'CORS POST测试数据',
      timestamp: Date.now()
    }
    const response = await axios.post('/api/test/cors/post', testData)
    addResult('POST 请求', '成功', 'success', response)
    ElMessage.success('POST请求测试成功')
  } catch (error: any) {
    addResult('POST 请求', `失败: ${error.message}`, 'danger', error.response?.data)
    ElMessage.error(`POST请求测试失败: ${error.message}`)
  } finally {
    testing.value.post = false
  }
}

// 测试OPTIONS请求
const testOptions = async () => {
  testing.value.options = true
  try {
    const response = await axios.options('/api/test/cors/options')
    addResult('OPTIONS 请求', '成功', 'success', response)
    ElMessage.success('OPTIONS请求测试成功')
  } catch (error: any) {
    addResult('OPTIONS 请求', `失败: ${error.message}`, 'danger', error.response?.data)
    ElMessage.error(`OPTIONS请求测试失败: ${error.message}`)
  } finally {
    testing.value.options = false
  }
}

// 获取请求头
const testHeaders = async () => {
  testing.value.headers = true
  try {
    const response = await axios.get('/api/test/cors/headers')
    addResult('请求头信息', '成功获取', 'info', response)
    ElMessage.success('获取请求头成功')
  } catch (error: any) {
    addResult('请求头信息', `失败: ${error.message}`, 'danger', error.response?.data)
    ElMessage.error(`获取请求头失败: ${error.message}`)
  } finally {
    testing.value.headers = false
  }
}

// 运行所有测试
const runAllTests = async () => {
  clearResults()
  await testGet()
  await new Promise(resolve => setTimeout(resolve, 500))
  await testPost()
  await new Promise(resolve => setTimeout(resolve, 500))
  await testOptions()
  await new Promise(resolve => setTimeout(resolve, 500))
  await testHeaders()
}

onMounted(() => {
  console.log('CORS测试页面已加载')
  console.log('前端地址:', frontendUrl.value)
  console.log('API Base URL:', apiBaseUrl.value)
  console.log('后端地址:', backendUrl.value)
})
</script>

<style scoped>
.cors-test {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mb-4 {
  margin-bottom: 20px;
}

.test-results {
  max-height: 500px;
  overflow-y: auto;
}

.test-result-item {
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.result-success {
  background-color: #f0f9ff;
  border-color: #67c23a;
}

.result-danger {
  background-color: #fef0f0;
  border-color: #f56c6c;
}

.result-warning {
  background-color: #fdf6ec;
  border-color: #e6a23c;
}

.result-info {
  background-color: #f4f4f5;
  border-color: #909399;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.timestamp {
  font-size: 12px;
  color: #909399;
}

.result-message {
  margin-bottom: 10px;
  font-weight: 500;
}

.result-data pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
