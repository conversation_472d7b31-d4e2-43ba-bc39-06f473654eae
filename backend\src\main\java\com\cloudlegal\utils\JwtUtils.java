package com.cloudlegal.utils;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.crypto.SecretKey;
import java.util.Date;
import java.util.Map;

/**
 * JWT工具类
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Component
public class JwtUtils {

    // JWT密钥（从配置文件读取，确保长度满足HS512要求）
    @Value("${jwt.secret}")
    private String jwtSecret;

    // JWT签发者
    @Value("${jwt.issuer:cloud-legal-system}")
    private String jwtIssuer;

    // JWT过期时间（毫秒）
    @Value("${jwt.expiration:86400000}")
    private long jwtExpiration;

    // JWT刷新Token过期时间（毫秒）
    @Value("${jwt.refresh-expiration:604800000}")
    private long jwtRefreshExpiration;

    /**
     * 生成JWT Token
     *
     * @param claims 载荷信息
     * @param expireMinutes 过期时间（分钟）
     * @return JWT Token
     */
    public String generateToken(Map<String, Object> claims, int expireMinutes) {
        Date now = new Date();
        Date expireDate = new Date(now.getTime() + expireMinutes * 60 * 1000L);

        return Jwts.builder()
                .setClaims(claims)
                .setIssuer(jwtIssuer)
                .setIssuedAt(now)
                .setExpiration(expireDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 生成JWT Token（使用默认过期时间）
     *
     * @param claims 载荷信息
     * @return JWT Token
     */
    public String generateToken(Map<String, Object> claims) {
        Date now = new Date();
        Date expireDate = new Date(now.getTime() + jwtExpiration);

        return Jwts.builder()
                .setClaims(claims)
                .setIssuer(jwtIssuer)
                .setIssuedAt(now)
                .setExpiration(expireDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 生成JWT Token（支持小时）
     *
     * @param claims 载荷信息
     * @param hours 小时数
     * @return JWT Token
     */
    public String generateTokenByHours(Map<String, Object> claims, long hours) {
        Date now = new Date();
        Date expireDate = new Date(now.getTime() + hours * 60 * 60 * 1000L);

        return Jwts.builder()
                .setClaims(claims)
                .setIssuer(jwtIssuer)
                .setIssuedAt(now)
                .setExpiration(expireDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 生成JWT Token（支持天数）
     *
     * @param claims 载荷信息
     * @param days 天数
     * @return JWT Token
     */
    public String generateTokenByDays(Map<String, Object> claims, long days) {
        Date now = new Date();
        Date expireDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000L);

        return Jwts.builder()
                .setClaims(claims)
                .setIssuer(jwtIssuer)
                .setIssuedAt(now)
                .setExpiration(expireDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从Token中获取载荷信息
     * 
     * @param token JWT Token
     * @return 载荷信息
     */
    public Map<String, Object> getClaimsFromToken(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            return claims;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从Token中获取用户ID
     * 
     * @param token JWT Token
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        Map<String, Object> claims = getClaimsFromToken(token);
        if (claims != null && claims.containsKey("userId")) {
            return Long.valueOf(claims.get("userId").toString());
        }
        return null;
    }

    /**
     * 从Token中获取用户名
     * 
     * @param token JWT Token
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        Map<String, Object> claims = getClaimsFromToken(token);
        if (claims != null && claims.containsKey("username")) {
            return claims.get("username").toString();
        }
        return null;
    }

    /**
     * 从Token中获取过期时间
     * 
     * @param token JWT Token
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            return claims.getExpiration();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 验证Token是否有效
     * 
     * @param token JWT Token
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token);
            return true;
        } catch (SecurityException e) {
            // 签名无效
            return false;
        } catch (MalformedJwtException e) {
            // Token格式错误
            return false;
        } catch (ExpiredJwtException e) {
            // Token已过期
            return false;
        } catch (UnsupportedJwtException e) {
            // 不支持的Token
            return false;
        } catch (IllegalArgumentException e) {
            // Token为空
            return false;
        }
    }

    /**
     * 检查Token是否过期
     * 
     * @param token JWT Token
     * @return 是否过期
     */
    public boolean isTokenExpired(String token) {
        Date expiration = getExpirationDateFromToken(token);
        return expiration != null && expiration.before(new Date());
    }

    /**
     * 刷新Token
     * 
     * @param token 原Token
     * @param expireMinutes 新的过期时间（分钟）
     * @return 新Token
     */
    public String refreshToken(String token, int expireMinutes) {
        Map<String, Object> claims = getClaimsFromToken(token);
        if (claims != null) {
            return generateToken(claims, expireMinutes);
        }
        return null;
    }

    /**
     * 获取Token剩余有效时间（秒）
     * 
     * @param token JWT Token
     * @return 剩余有效时间（秒），-1表示已过期或无效
     */
    public long getTokenRemainingTime(String token) {
        Date expiration = getExpirationDateFromToken(token);
        if (expiration != null) {
            long remaining = expiration.getTime() - System.currentTimeMillis();
            return remaining > 0 ? remaining / 1000 : -1;
        }
        return -1;
    }

    /**
     * 获取签名密钥
     *
     * @return 签名密钥
     */
    private SecretKey getSigningKey() {
        byte[] keyBytes;

        try {
            // 尝试将密钥作为Base64解码
            keyBytes = java.util.Base64.getDecoder().decode(jwtSecret);
        } catch (IllegalArgumentException e) {
            // 如果不是Base64，则作为普通字符串处理
            keyBytes = jwtSecret.getBytes(java.nio.charset.StandardCharsets.UTF_8);
        }

        // 验证密钥长度
        if (keyBytes.length < 64) {
            throw new IllegalArgumentException(
                String.format("JWT密钥长度不足，当前长度：%d字节（%d位），HS512算法要求至少64字节（512位）",
                    keyBytes.length, keyBytes.length * 8)
            );
        }

        return Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * 获取Token剩余有效时间（分钟）
     *
     * @param token JWT Token
     * @return 剩余有效时间（分钟），-1表示已过期或无效
     */
    public long getTokenRemainingTimeInMinutes(String token) {
        Date expiration = getExpirationDateFromToken(token);
        if (expiration != null) {
            long remainingMillis = expiration.getTime() - System.currentTimeMillis();
            return remainingMillis > 0 ? remainingMillis / (60 * 1000L) : -1;
        }
        return -1;
    }

    /**
     * 获取Token剩余有效时间（小时）
     *
     * @param token JWT Token
     * @return 剩余有效时间（小时），-1表示已过期或无效
     */
    public long getTokenRemainingTimeInHours(String token) {
        Date expiration = getExpirationDateFromToken(token);
        if (expiration != null) {
            long remainingMillis = expiration.getTime() - System.currentTimeMillis();
            return remainingMillis > 0 ? remainingMillis / (60 * 60 * 1000L) : -1;
        }
        return -1;
    }

    /**
     * 解析Token头部信息
     *
     * @param token JWT Token
     * @return 头部信息
     */
    public Map<String, Object> getHeaderFromToken(String token) {
        try {
            String[] chunks = token.split("\\.");
            if (chunks.length >= 2) {
                // 这里简化处理，实际应该使用JSON解析
                return Map.of("alg", "HS512", "typ", "JWT");
            }
        } catch (Exception e) {
            // 解析失败
        }
        return null;
    }

    /**
     * 创建Token用于测试
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param roleId 角色ID
     * @param expireMinutes 过期时间（分钟）
     * @return JWT Token
     */
    public String createTestToken(Long userId, String username, Long roleId, int expireMinutes) {
        Map<String, Object> claims = Map.of(
            "userId", userId,
            "username", username,
            "roleId", roleId
        );
        return generateToken(claims, expireMinutes);
    }

    /**
     * 创建Token用于测试（支持TimeUnit）
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param roleId 角色ID
     * @param duration 过期时间数值
     * @param timeUnit 时间单位
     * @return JWT Token
     */
    public String createTestToken(Long userId, String username, Long roleId, long duration) {
        Map<String, Object> claims = Map.of(
            "userId", userId,
            "username", username,
            "roleId", roleId
        );
        return generateToken(claims, (int) duration);
    }

    /**
     * 生成访问Token（24小时有效期）
     *
     * @param claims 载荷信息
     * @return JWT Token
     */
    public String generateAccessToken(Map<String, Object> claims) {
        return generateToken(claims, 24);
    }

    /**
     * 生成刷新Token（7天有效期）
     *
     * @param claims 载荷信息
     * @return JWT Token
     */
    public String generateRefreshToken(Map<String, Object> claims) {
        return generateToken(claims, 7);
    }

    /**
     * 验证密钥强度
     *
     * @return 密钥是否满足安全要求
     */
    public boolean validateKeyStrength() {
        try {
            byte[] keyBytes = jwtSecret.getBytes(java.nio.charset.StandardCharsets.UTF_8);
            return keyBytes.length >= 64; // HS512要求至少64字节
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取密钥信息
     *
     * @return 密钥信息
     */
    public Map<String, Object> getKeyInfo() {
        byte[] keyBytes;
        boolean isBase64 = false;

        try {
            // 尝试将密钥作为Base64解码
            keyBytes = java.util.Base64.getDecoder().decode(jwtSecret);
            isBase64 = true;
        } catch (IllegalArgumentException e) {
            // 如果不是Base64，则作为普通字符串处理
            keyBytes = jwtSecret.getBytes(java.nio.charset.StandardCharsets.UTF_8);
        }

        return Map.of(
            "keyLength", keyBytes.length,
            "keyLengthBits", keyBytes.length * 8,
            "algorithm", "HS512",
            "minRequiredBits", 512,
            "isSecure", keyBytes.length >= 64,
            "isBase64Encoded", isBase64,
            "keyFormat", isBase64 ? "Base64" : "String"
        );
    }
}
