<template>
  <div class="proxy-test">
    <el-card header="代理配置测试">
      <div class="test-section">
        <h3>环境信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="前端地址">{{ frontendUrl }}</el-descriptions-item>
          <el-descriptions-item label="API基础URL">{{ apiBaseUrl }}</el-descriptions-item>
          <el-descriptions-item label="后端地址">{{ backendUrl }}</el-descriptions-item>
          <el-descriptions-item label="Mock模式">{{ mockEnabled ? '启用' : '禁用' }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="test-section">
        <h3>接口测试</h3>
        <el-space direction="vertical" style="width: 100%">
          <el-button @click="testHealthCheck" :loading="testing.health">
            测试健康检查
          </el-button>
          <el-button @click="testLogin" :loading="testing.login">
            测试登录接口
          </el-button>
          <el-button @click="testUserInfo" :loading="testing.userInfo">
            测试用户信息接口
          </el-button>
        </el-space>
      </div>

      <div class="test-section">
        <h3>测试结果</h3>
        <el-timeline>
          <el-timeline-item
            v-for="result in testResults"
            :key="result.id"
            :type="result.type"
            :timestamp="result.timestamp"
          >
            <strong>{{ result.title }}</strong>
            <p>{{ result.message }}</p>
            <pre v-if="result.data">{{ JSON.stringify(result.data, null, 2) }}</pre>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'

// 响应式数据
const frontendUrl = ref(window.location.origin)
const apiBaseUrl = ref(import.meta.env.VITE_API_BASE_URL || '/api')
const backendUrl = ref(import.meta.env.VITE_BACKEND_URL || 'http://localhost:8080')
const mockEnabled = ref(import.meta.env.VITE_ENABLE_MOCK !== 'false')

const testing = ref({
  health: false,
  login: false,
  userInfo: false
})

const testResults = ref<Array<{
  id: number
  title: string
  message: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  timestamp: string
  data?: any
}>>([])

// 添加测试结果
const addResult = (title: string, message: string, type: any, data?: any) => {
  testResults.value.unshift({
    id: Date.now(),
    title,
    message,
    type,
    timestamp: new Date().toLocaleTimeString(),
    data
  })
}

// 测试健康检查
const testHealthCheck = async () => {
  testing.value.health = true
  try {
    const response = await axios.get('/api/health', { timeout: 5000 })
    addResult('健康检查', '接口调用成功', 'success', response.data)
    ElMessage.success('健康检查通过')
  } catch (error: any) {
    const message = error.response?.status === 404 
      ? '接口不存在，但代理配置正常' 
      : error.message
    addResult('健康检查', message, error.response?.status === 404 ? 'warning' : 'danger', error.response?.data)
    if (error.response?.status === 404) {
      ElMessage.warning('接口不存在，但代理配置正常')
    } else {
      ElMessage.error('健康检查失败: ' + error.message)
    }
  } finally {
    testing.value.health = false
  }
}

// 测试登录接口
const testLogin = async () => {
  testing.value.login = true
  try {
    const response = await axios.post('/api/auth/login', {
      username: 'test',
      password: 'test'
    }, { timeout: 5000 })
    addResult('登录接口', '接口调用成功', 'success', response.data)
    ElMessage.success('登录接口测试成功')
  } catch (error: any) {
    const message = error.response?.status === 401 
      ? '认证失败，但代理配置正常' 
      : error.message
    addResult('登录接口', message, error.response?.status === 401 ? 'warning' : 'danger', error.response?.data)
    if (error.response?.status === 401) {
      ElMessage.warning('认证失败，但代理配置正常')
    } else {
      ElMessage.error('登录接口测试失败: ' + error.message)
    }
  } finally {
    testing.value.login = false
  }
}

// 测试用户信息接口
const testUserInfo = async () => {
  testing.value.userInfo = true
  try {
    const response = await axios.get('/api/user/info', { timeout: 5000 })
    addResult('用户信息接口', '接口调用成功', 'success', response.data)
    ElMessage.success('用户信息接口测试成功')
  } catch (error: any) {
    const message = error.response?.status === 401 
      ? '需要认证，但代理配置正常' 
      : error.message
    addResult('用户信息接口', message, error.response?.status === 401 ? 'warning' : 'danger', error.response?.data)
    if (error.response?.status === 401) {
      ElMessage.warning('需要认证，但代理配置正常')
    } else {
      ElMessage.error('用户信息接口测试失败: ' + error.message)
    }
  } finally {
    testing.value.userInfo = false
  }
}

onMounted(() => {
  addResult('页面加载', '代理测试页面已加载', 'info')
})
</script>

<style scoped>
.proxy-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  margin-bottom: 15px;
  color: #409eff;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
