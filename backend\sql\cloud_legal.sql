-- 云法务系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS cloud_legal DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE cloud_legal;

-- 用户表
CREATE TABLE sys_user (
    id BIGINT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(255) COMMENT '头像',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    role_id BIGINT COMMENT '角色ID',
    dept_id BIGINT COMMENT '部门ID',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    created_by BIGINT COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
) COMMENT '用户表';

-- 角色表
CREATE TABLE sys_role (
    id BIGINT PRIMARY KEY COMMENT '角色ID',
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description VARCHAR(255) COMMENT '角色描述',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    created_by BIGINT COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
) COMMENT '角色表';

-- 权限表
CREATE TABLE sys_permission (
    id BIGINT PRIMARY KEY COMMENT '权限ID',
    permission_name VARCHAR(50) NOT NULL COMMENT '权限名称',
    permission_code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    permission_type TINYINT NOT NULL COMMENT '权限类型：1-菜单，2-按钮',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    path VARCHAR(255) COMMENT '路由路径',
    component VARCHAR(255) COMMENT '组件路径',
    icon VARCHAR(50) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    created_by BIGINT COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
) COMMENT '权限表';

-- 角色权限关联表
CREATE TABLE sys_role_permission (
    id BIGINT PRIMARY KEY COMMENT 'ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_role_permission (role_id, permission_id)
) COMMENT '角色权限关联表';

-- 客户表
CREATE TABLE biz_client (
    id BIGINT PRIMARY KEY COMMENT '客户ID',
    client_name VARCHAR(100) NOT NULL COMMENT '客户名称',
    client_type TINYINT NOT NULL COMMENT '客户类型：1-个人，2-企业，3-组织',
    contact_person VARCHAR(50) COMMENT '联系人',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    address VARCHAR(255) COMMENT '地址',
    industry VARCHAR(50) COMMENT '所属行业',
    credit_code VARCHAR(50) COMMENT '统一社会信用代码',
    legal_person VARCHAR(50) COMMENT '法定代表人',
    registered_capital DECIMAL(15,2) COMMENT '注册资本',
    status TINYINT DEFAULT 1 COMMENT '状态：0-停用，1-正常',
    description TEXT COMMENT '客户描述',
    created_by BIGINT COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
) COMMENT '客户表';

-- 案件表
CREATE TABLE biz_case (
    id BIGINT PRIMARY KEY COMMENT '案件ID',
    case_number VARCHAR(50) NOT NULL UNIQUE COMMENT '案件编号',
    case_title VARCHAR(200) NOT NULL COMMENT '案件标题',
    case_type VARCHAR(50) NOT NULL COMMENT '案件类型',
    case_status VARCHAR(20) NOT NULL COMMENT '案件状态',
    priority_level VARCHAR(20) NOT NULL COMMENT '优先级',
    client_id BIGINT NOT NULL COMMENT '客户ID',
    client_name VARCHAR(100) COMMENT '客户名称',
    lawyer_id BIGINT COMMENT '负责律师ID',
    lawyer_name VARCHAR(50) COMMENT '负责律师姓名',
    case_amount DECIMAL(15,2) COMMENT '案件金额',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    court_name VARCHAR(100) COMMENT '审理法院',
    case_description TEXT COMMENT '案件描述',
    created_by BIGINT COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
) COMMENT '案件表';

-- 合同表
CREATE TABLE biz_contract (
    id BIGINT PRIMARY KEY COMMENT '合同ID',
    contract_number VARCHAR(50) NOT NULL UNIQUE COMMENT '合同编号',
    contract_title VARCHAR(200) NOT NULL COMMENT '合同标题',
    contract_type VARCHAR(50) NOT NULL COMMENT '合同类型',
    contract_status VARCHAR(20) NOT NULL COMMENT '合同状态',
    client_id BIGINT NOT NULL COMMENT '客户ID',
    client_name VARCHAR(100) COMMENT '客户名称',
    contract_amount DECIMAL(15,2) COMMENT '合同金额',
    sign_date DATE COMMENT '签署日期',
    start_date DATE COMMENT '生效日期',
    end_date DATE COMMENT '到期日期',
    contract_content TEXT COMMENT '合同内容',
    created_by BIGINT COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
) COMMENT '合同表';

-- 文档表
CREATE TABLE biz_document (
    id BIGINT PRIMARY KEY COMMENT '文档ID',
    document_name VARCHAR(200) NOT NULL COMMENT '文档名称',
    document_type VARCHAR(20) NOT NULL COMMENT '文档类型',
    file_size BIGINT COMMENT '文件大小',
    file_path VARCHAR(500) COMMENT '文件路径',
    file_url VARCHAR(500) COMMENT '文件URL',
    category VARCHAR(50) COMMENT '文档分类',
    case_id BIGINT COMMENT '关联案件ID',
    contract_id BIGINT COMMENT '关联合同ID',
    uploader_id BIGINT COMMENT '上传人ID',
    uploader_name VARCHAR(50) COMMENT '上传人姓名',
    created_by BIGINT COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
) COMMENT '文档表';

-- 财务记录表
CREATE TABLE biz_finance_record (
    id BIGINT PRIMARY KEY COMMENT '财务记录ID',
    record_type TINYINT NOT NULL COMMENT '记录类型：1-收入，2-支出',
    category VARCHAR(50) NOT NULL COMMENT '分类',
    amount DECIMAL(15,2) NOT NULL COMMENT '金额',
    description VARCHAR(255) COMMENT '描述',
    record_date DATE NOT NULL COMMENT '记录日期',
    case_id BIGINT COMMENT '关联案件ID',
    contract_id BIGINT COMMENT '关联合同ID',
    created_by BIGINT COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除'
) COMMENT '财务记录表';

-- 系统日志表
CREATE TABLE sys_log (
    id BIGINT PRIMARY KEY COMMENT '日志ID',
    operation VARCHAR(100) COMMENT '操作',
    method VARCHAR(200) COMMENT '请求方法',
    params TEXT COMMENT '请求参数',
    time BIGINT COMMENT '执行时长(毫秒)',
    ip VARCHAR(50) COMMENT 'IP地址',
    user_id BIGINT COMMENT '用户ID',
    username VARCHAR(50) COMMENT '用户名',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '系统日志表';

-- 创建索引
CREATE INDEX idx_user_username ON sys_user(username);
CREATE INDEX idx_user_email ON sys_user(email);
CREATE INDEX idx_user_phone ON sys_user(phone);
CREATE INDEX idx_client_name ON biz_client(client_name);
CREATE INDEX idx_client_type ON biz_client(client_type);
CREATE INDEX idx_case_number ON biz_case(case_number);
CREATE INDEX idx_case_client ON biz_case(client_id);
CREATE INDEX idx_case_lawyer ON biz_case(lawyer_id);
CREATE INDEX idx_case_status ON biz_case(case_status);
CREATE INDEX idx_contract_number ON biz_contract(contract_number);
CREATE INDEX idx_contract_client ON biz_contract(client_id);
CREATE INDEX idx_contract_status ON biz_contract(contract_status);
CREATE INDEX idx_document_case ON biz_document(case_id);
CREATE INDEX idx_document_contract ON biz_document(contract_id);
CREATE INDEX idx_finance_type ON biz_finance_record(record_type);
CREATE INDEX idx_finance_date ON biz_finance_record(record_date);
CREATE INDEX idx_log_user ON sys_log(user_id);
CREATE INDEX idx_log_time ON sys_log(created_time);

-- 插入初始数据
-- 插入角色数据
INSERT INTO sys_role (id, role_name, role_code, description, status, created_time) VALUES
(1, '超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限', 1, NOW()),
(2, '管理员', 'ADMIN', '系统管理员', 1, NOW()),
(3, '律师', 'LAWYER', '执业律师', 1, NOW()),
(4, '助理', 'ASSISTANT', '律师助理', 1, NOW()),
(5, '客户', 'CLIENT', '客户用户', 1, NOW());

-- 插入用户数据（密码都是123456，已加密）
INSERT INTO sys_user (id, username, password, real_name, email, phone, status, role_id, created_time) VALUES
(1, 'admin', '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKloYAcHqoOX9IbkMUBvQjkrOm2O', '系统管理员', '<EMAIL>', '13800000001', 1, 1, NOW()),
(2, 'lawyer1', '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKloYAcHqoOX9IbkMUBvQjkrOm2O', '张律师', '<EMAIL>', '13800000002', 1, 3, NOW()),
(3, 'assistant1', '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKloYAcHqoOX9IbkMUBvQjkrOm2O', '李助理', '<EMAIL>', '13800000003', 1, 4, NOW()),
(4, 'lawyer2', '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKloYAcHqoOX9IbkMUBvQjkrOm2O', '赵律师', '<EMAIL>', '13800000004', 1, 3, NOW()),
(5, 'assistant2', '$2a$10$7JB720yubVSOfvVWdBYoOe.PuiKloYAcHqoOX9IbkMUBvQjkrOm2O', '王助理', '<EMAIL>', '13800000005', 1, 4, NOW());

-- 插入客户数据
INSERT INTO biz_client (id, client_name, client_type, contact_person, phone, email, address, industry, credit_code, legal_person, registered_capital, status, description, created_time) VALUES
(1, '北京科技有限公司', 2, '张总', '13800138001', '<EMAIL>', '北京市海淀区中关村大街1号', '软件开发', '91110000123456789A', '张三', 1000000.00, 1, '专注于人工智能技术研发的高新技术企业', NOW()),
(2, '李先生', 1, '李先生', '13800138002', '<EMAIL>', '上海市浦东新区陆家嘴金融区', '', '', '', NULL, 1, '房地产投资客户', NOW()),
(3, '王女士', 1, '王女士', '13800138003', '<EMAIL>', '广州市天河区珠江新城', '', '', '', NULL, 1, '劳动纠纷当事人', NOW()),
(4, '上海投资集团', 2, '刘总监', '13800138004', '<EMAIL>', '上海市黄浦区南京东路100号', '投资管理', '91310000123456789B', '刘四', 50000000.00, 1, '大型投资管理公司，业务涵盖股权投资、并购重组等', NOW()),
(5, '深圳创新科技', 2, '赵总经理', '13800138006', '<EMAIL>', '深圳市南山区高新技术产业园', '科技创新', '91440300123456789C', '赵五', 2000000.00, 1, '专注于新兴技术研发和产业化应用的创新型企业', NOW());

-- 插入案件数据
INSERT INTO biz_case (id, case_number, case_title, case_type, case_status, priority_level, client_id, client_name, lawyer_id, lawyer_name, case_amount, start_date, court_name, case_description, created_time) VALUES
(1, 'CASE2024001', '某科技公司知识产权纠纷案', 'intellectual', 'processing', 'high', 1, '北京科技有限公司', 2, '张律师', 500000.00, '2024-01-01', '北京知识产权法院', '涉及专利侵权纠纷，需要进行专利无效宣告程序', NOW()),
(2, 'CASE2024002', '房地产买卖合同纠纷', 'contract', 'pending', 'medium', 2, '李先生', 2, '张律师', 200000.00, '2024-01-15', '上海市第一中级人民法院', '购房合同违约，要求退房退款', NOW()),
(3, 'CASE2024003', '劳动争议仲裁案', 'labor', 'completed', 'low', 3, '王女士', 3, '李助理', 80000.00, '2023-11-01', '广州市劳动争议仲裁委员会', '工伤赔偿纠纷，已达成调解协议', NOW()),
(4, 'CASE2024004', '公司股权转让纠纷', 'corporate', 'processing', 'urgent', 4, '上海投资集团', 2, '张律师', 1200000.00, '2024-01-10', '上海市高级人民法院', '股权转让协议争议，涉及估值分歧', NOW()),
(5, 'CASE2024005', '商标侵权诉讼案', 'intellectual', 'processing', 'high', 5, '深圳创新科技', 4, '赵律师', 350000.00, '2024-01-05', '深圳市中级人民法院', '竞争对手恶意抢注商标，要求撤销并赔偿损失', NOW());

-- 插入合同数据
INSERT INTO biz_contract (id, contract_number, contract_title, contract_type, contract_status, client_id, client_name, contract_amount, sign_date, start_date, end_date, contract_content, created_time) VALUES
(1, '*********', '软件开发服务合同', 'service', 'signed', 1, '北京科技有限公司', 300000.00, '2024-01-20', '2024-01-20', '2024-04-20', '软件开发服务合同内容...', NOW()),
(2, '*********', '法律顾问服务协议', 'service', 'approved', 4, '上海投资集团', 150000.00, NULL, '2024-02-01', '2025-01-31', '法律顾问服务协议内容...', NOW()),
(3, '*********', '知识产权许可协议', 'license', 'signed', 5, '深圳创新科技', 500000.00, '2023-12-01', '2023-12-01', '2024-11-30', '知识产权许可协议内容...', NOW()),
(4, '*********', '投资咨询服务合同', 'consulting', 'draft', 4, '上海投资集团', 800000.00, NULL, '2024-03-01', '2025-02-28', '投资咨询服务合同内容...', NOW()),
(5, '*********', '技术转让合同', 'transfer', 'reviewing', 5, '深圳创新科技', 1200000.00, NULL, '2024-02-15', '2026-02-14', '技术转让合同内容...', NOW());

-- 插入财务记录数据
INSERT INTO biz_finance_record (id, record_type, category, amount, description, record_date, case_id, contract_id, created_time) VALUES
(1, 1, 'lawyer_fee', 50000.00, '某科技公司知识产权纠纷案律师费', '2024-01-10', 1, NULL, NOW()),
(2, 1, 'consulting_fee', 15000.00, '法律咨询服务费', '2024-01-15', NULL, 2, NOW()),
(3, 2, 'office_expense', 8000.00, '办公用品采购', '2024-01-20', NULL, NULL, NOW()),
(4, 2, 'travel_expense', 3500.00, '出差调研费用', '2024-01-25', 2, NULL, NOW()),
(5, 1, 'lawyer_fee', 80000.00, '公司股权转让纠纷案件费用', '2024-01-05', 4, NULL, NOW()),
(6, 1, 'lawyer_fee', 35000.00, '商标侵权诉讼案代理费', '2024-01-25', 5, NULL, NOW()),
(7, 1, 'consulting_fee', 120000.00, '建设工程法律服务费', '2023-12-15', NULL, 1, NOW()),
(8, 2, 'court_fee', 5000.00, '离婚案件诉讼费', '2023-11-20', 3, NULL, NOW()),
(9, 1, 'license_fee', 60000.00, '知识产权许可协议服务费', '2023-12-20', NULL, 3, NOW()),
(10, 2, 'office_expense', 15000.00, '办公设备维护费', '2024-01-30', NULL, NULL, NOW());
