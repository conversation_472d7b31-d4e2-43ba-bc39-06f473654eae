import { generateId, generateDate, randomChoice, randomNumber } from './data'

// 随机姓名生成器
const surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗']
const givenNames = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞']

const generateRandomName = () => {
  return randomChoice(surnames) + randomChoice(givenNames) + (Math.random() > 0.7 ? randomChoice(givenNames) : '')
}

// 公司名称生成器
const companyPrefixes = ['北京', '上海', '深圳', '广州', '杭州', '成都', '武汉', '西安', '南京', '天津']
const companyTypes = ['科技', '投资', '建筑', '贸易', '制造', '服务', '咨询', '文化', '教育', '医疗']
const companySuffixes = ['有限公司', '股份有限公司', '集团有限公司', '科技有限公司', '投资有限公司']

const generateCompanyName = () => {
  return randomChoice(companyPrefixes) + randomChoice(companyTypes) + randomChoice(companySuffixes)
}

// 案件类型和标题生成器
const caseTypes = [
  { type: 'intellectual', titles: ['专利侵权纠纷', '商标权纠纷', '著作权纠纷', '商业秘密纠纷', '不正当竞争纠纷'] },
  { type: 'contract', titles: ['买卖合同纠纷', '服务合同纠纷', '租赁合同纠纷', '建设工程合同纠纷', '技术合同纠纷'] },
  { type: 'labor', titles: ['劳动争议', '工伤赔偿纠纷', '劳动合同纠纷', '社保争议', '加班费纠纷'] },
  { type: 'civil', titles: ['离婚纠纷', '继承纠纷', '名誉权纠纷', '隐私权纠纷', '人身损害赔偿'] },
  { type: 'corporate', titles: ['股权转让纠纷', '公司治理纠纷', '股东权益纠纷', '公司并购', '投资纠纷'] },
  { type: 'consumer', titles: ['产品质量纠纷', '消费者权益纠纷', '虚假广告纠纷', '价格欺诈', '售后服务纠纷'] }
]

// 合同类型生成器
const contractTypes = [
  { type: 'service', titles: ['法律服务合同', '咨询服务合同', '技术服务合同', '管理服务合同'] },
  { type: 'purchase', titles: ['设备采购合同', '软件采购合同', '办公用品采购合同', '原材料采购合同'] },
  { type: 'license', titles: ['软件许可协议', '专利许可协议', '商标许可协议', '技术许可协议'] },
  { type: 'construction', titles: ['建筑施工合同', '装修工程合同', '设计服务合同', '监理服务合同'] },
  { type: 'consulting', titles: ['管理咨询合同', '财务咨询合同', '法律咨询合同', '技术咨询合同'] },
  { type: 'agency', titles: ['销售代理协议', '品牌代理协议', '进出口代理协议', '招商代理协议'] },
  { type: 'transfer', titles: ['技术转让合同', '股权转让协议', '资产转让合同', '知识产权转让协议'] }
]

// 生成随机客户
export const generateRandomClient = () => {
  const isCompany = Math.random() > 0.3
  const industries = ['科技', '金融', '制造', '贸易', '服务', '教育', '医疗', '文化', '建筑', '农业']
  
  if (isCompany) {
    return {
      id: generateId(),
      name: generateCompanyName(),
      type: 'company',
      contact: generateRandomName() + '总',
      phone: `138${randomNumber(10000000, 99999999)}`,
      email: `contact@${Math.random().toString(36).substr(2, 8)}.com`,
      address: `${randomChoice(companyPrefixes)}市${randomChoice(['朝阳区', '海淀区', '浦东新区', '天河区', '南山区'])}${randomChoice(['科技园', '商务区', '工业园', '金融街'])}`,
      industry: randomChoice(industries),
      status: randomChoice(['active', 'inactive']),
      description: `专业的${randomChoice(industries)}企业，业务涵盖多个领域`,
      createdAt: generateDate(randomNumber(30, 365)),
      updatedAt: generateDate(randomNumber(1, 30))
    }
  } else {
    return {
      id: generateId(),
      name: generateRandomName() + randomChoice(['先生', '女士']),
      type: 'individual',
      contact: generateRandomName() + randomChoice(['先生', '女士']),
      phone: `138${randomNumber(10000000, 99999999)}`,
      email: `${Math.random().toString(36).substr(2, 8)}@email.com`,
      address: `${randomChoice(companyPrefixes)}市${randomChoice(['朝阳区', '海淀区', '浦东新区', '天河区', '南山区'])}${randomChoice(['小区', '大厦', '公寓', '别墅区'])}`,
      industry: '',
      status: 'active',
      description: `个人客户，涉及${randomChoice(['房产', '投资', '劳动', '民事', '消费'])}相关法律事务`,
      createdAt: generateDate(randomNumber(30, 365)),
      updatedAt: generateDate(randomNumber(1, 30))
    }
  }
}

// 生成随机案件
export const generateRandomCase = (clientId?: string, clientName?: string) => {
  const caseTypeData = randomChoice(caseTypes)
  const caseTitle = randomChoice(caseTypeData.titles)
  const priorities = ['low', 'medium', 'high', 'urgent']
  const statuses = ['pending', 'processing', 'completed', 'closed']
  const lawyers = ['张律师', '李助理', '赵律师', '王律师', '陈律师']
  
  return {
    id: generateId(),
    caseNumber: `CASE${new Date().getFullYear()}${String(randomNumber(100, 999)).padStart(3, '0')}`,
    title: caseTitle + '案',
    type: caseTypeData.type,
    status: randomChoice(statuses),
    priority: randomChoice(priorities),
    clientId: clientId || generateId(),
    clientName: clientName || generateCompanyName(),
    lawyerId: String(randomNumber(2, 5)),
    lawyerName: randomChoice(lawyers),
    description: `涉及${caseTitle}相关法律问题，需要专业法律服务`,
    amount: randomNumber(50000, 2000000),
    startDate: generateDate(randomNumber(5, 60)),
    endDate: Math.random() > 0.6 ? generateDate(randomNumber(-30, 0)) : null,
    createdAt: generateDate(randomNumber(5, 60)),
    updatedAt: generateDate(randomNumber(1, 5))
  }
}

// 生成随机合同
export const generateRandomContract = (clientId?: string, clientName?: string) => {
  const contractTypeData = randomChoice(contractTypes)
  const contractTitle = randomChoice(contractTypeData.titles)
  const statuses = ['draft', 'reviewing', 'approved', 'signed', 'expired']
  
  return {
    id: generateId(),
    contractNumber: `CT${new Date().getFullYear()}${String(randomNumber(100, 999)).padStart(3, '0')}`,
    title: contractTitle,
    type: contractTypeData.type,
    status: randomChoice(statuses),
    clientId: clientId || generateId(),
    clientName: clientName || generateCompanyName(),
    amount: randomNumber(100000, 5000000),
    signDate: Math.random() > 0.4 ? generateDate(randomNumber(10, 100)) : null,
    startDate: generateDate(randomNumber(-30, 30)),
    endDate: generateDate(randomNumber(-365, -30)),
    createdAt: generateDate(randomNumber(10, 100)),
    updatedAt: generateDate(randomNumber(1, 10))
  }
}

// 生成随机文档
export const generateRandomDocument = () => {
  const fileTypes = [
    { ext: 'pdf', category: '案件材料' },
    { ext: 'docx', category: '合同模板' },
    { ext: 'xlsx', category: '财务文档' },
    { ext: 'jpg', category: '客户资料' },
    { ext: 'png', category: '案件材料' },
    { ext: 'pptx', category: '客户资料' }
  ]
  
  const fileType = randomChoice(fileTypes)
  const uploaders = ['张律师', '李助理', '赵律师', '系统管理员']
  const docNames = [
    '法律文书', '证据材料', '合同模板', '财务报表', '客户资料',
    '调查报告', '鉴定报告', '协议书', '授权书', '证明文件'
  ]
  
  return {
    id: generateId(),
    name: `${randomChoice(docNames)}.${fileType.ext}`,
    type: fileType.ext,
    size: randomNumber(100000, 5000000),
    category: fileType.category,
    uploaderName: randomChoice(uploaders),
    caseTitle: Math.random() > 0.5 ? '相关案件' : '',
    contractTitle: Math.random() > 0.5 ? '相关合同' : '',
    url: `/mock/documents/${Math.random().toString(36).substr(2, 8)}.${fileType.ext}`,
    createdAt: generateDate(randomNumber(1, 100))
  }
}

// 生成随机财务记录
export const generateRandomFinanceRecord = () => {
  const types = ['income', 'expense']
  const incomeCategories = ['lawyer_fee', 'consulting_fee', 'service_fee', 'license_fee']
  const expenseCategories = ['office_expense', 'travel_expense', 'court_fee', 'expert_fee', 'software_license']
  
  const type = randomChoice(types)
  const category = type === 'income' ? randomChoice(incomeCategories) : randomChoice(expenseCategories)
  
  const descriptions = {
    lawyer_fee: ['案件代理费', '法律服务费', '诉讼代理费'],
    consulting_fee: ['法律咨询费', '专业咨询费', '顾问服务费'],
    service_fee: ['专业服务费', '技术服务费', '管理服务费'],
    license_fee: ['许可使用费', '授权费', '特许费'],
    office_expense: ['办公用品费', '设备维护费', '租金费用'],
    travel_expense: ['差旅费', '交通费', '住宿费'],
    court_fee: ['诉讼费', '仲裁费', '公证费'],
    expert_fee: ['专家咨询费', '鉴定费', '评估费'],
    software_license: ['软件许可费', '系统使用费', '平台费']
  }
  
  return {
    id: generateId(),
    type,
    category,
    amount: type === 'income' ? randomNumber(10000, 200000) : randomNumber(1000, 50000),
    description: randomChoice(descriptions[category as keyof typeof descriptions]),
    date: generateDate(randomNumber(1, 365)),
    caseId: Math.random() > 0.5 ? generateId() : '',
    caseTitle: Math.random() > 0.5 ? '相关案件' : '',
    contractId: Math.random() > 0.5 ? generateId() : '',
    contractTitle: Math.random() > 0.5 ? '相关合同' : '',
    createdAt: generateDate(randomNumber(1, 365))
  }
}

// 批量生成数据
export const generateBatchData = (count: number) => {
  return {
    clients: Array.from({ length: count }, () => generateRandomClient()),
    cases: Array.from({ length: count }, () => generateRandomCase()),
    contracts: Array.from({ length: count }, () => generateRandomContract()),
    documents: Array.from({ length: count }, () => generateRandomDocument()),
    financeRecords: Array.from({ length: count }, () => generateRandomFinanceRecord())
  }
}
