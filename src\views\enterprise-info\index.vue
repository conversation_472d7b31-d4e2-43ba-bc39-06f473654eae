<template>
  <div class="enterprise-info-page">
    <div class="page-header">
      <h1>企业信息</h1>
      <p>企业查询、风险监测、尽调报告</p>
    </div>

    <div class="search-section">
      <div class="search-card">
        <h2>企业信息查询</h2>
        <div class="search-form">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入企业名称或统一社会信用代码"
            size="large"
            @keyup.enter="searchEnterprise"
          >
            <template #append>
              <el-button type="primary" @click="searchEnterprise" :loading="isSearching">
                查询
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <div v-if="searchResult" class="result-section">
      <div class="enterprise-card">
        <div class="enterprise-header">
          <div class="enterprise-basic">
            <h2>{{ searchResult.name }}</h2>
            <div class="enterprise-tags">
              <el-tag :type="getStatusType(searchResult.status)">
                {{ searchResult.status }}
              </el-tag>
              <el-tag type="info">{{ searchResult.type }}</el-tag>
            </div>
          </div>
          <div class="enterprise-actions">
            <el-button type="primary" @click="generateReport">
              生成尽调报告
            </el-button>
            <el-button @click="startMonitoring">
              开启监测
            </el-button>
          </div>
        </div>

        <div class="enterprise-content">
          <el-tabs v-model="activeTab">
            <el-tab-pane label="基本信息" name="basic">
              <div class="info-grid">
                <div class="info-item">
                  <label>统一社会信用代码：</label>
                  <span>{{ searchResult.creditCode }}</span>
                </div>
                <div class="info-item">
                  <label>法定代表人：</label>
                  <span>{{ searchResult.legalPerson }}</span>
                </div>
                <div class="info-item">
                  <label>注册资本：</label>
                  <span>{{ searchResult.registeredCapital }}</span>
                </div>
                <div class="info-item">
                  <label>成立日期：</label>
                  <span>{{ searchResult.establishDate }}</span>
                </div>
                <div class="info-item">
                  <label>经营状态：</label>
                  <span>{{ searchResult.businessStatus }}</span>
                </div>
                <div class="info-item">
                  <label>所属行业：</label>
                  <span>{{ searchResult.industry }}</span>
                </div>
                <div class="info-item full-width">
                  <label>注册地址：</label>
                  <span>{{ searchResult.address }}</span>
                </div>
                <div class="info-item full-width">
                  <label>经营范围：</label>
                  <span>{{ searchResult.businessScope }}</span>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="风险信息" name="risk">
              <div class="risk-overview">
                <div class="risk-score">
                  <div class="score-circle">
                    <span class="score-number">{{ searchResult.riskScore }}</span>
                    <span class="score-label">风险评分</span>
                  </div>
                  <div class="score-desc">
                    <p>综合风险等级：<span :class="getRiskLevel(searchResult.riskScore)">{{ getRiskLevelText(searchResult.riskScore) }}</span></p>
                  </div>
                </div>
                
                <div class="risk-items">
                  <div class="risk-item" v-for="risk in searchResult.risks" :key="risk.type">
                    <div class="risk-header">
                      <span class="risk-title">{{ risk.title }}</span>
                      <el-tag :type="risk.level === 'high' ? 'danger' : risk.level === 'medium' ? 'warning' : 'success'" size="small">
                        {{ risk.count }}条
                      </el-tag>
                    </div>
                    <p class="risk-desc">{{ risk.description }}</p>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="关联企业" name="related">
              <div class="related-enterprises">
                <div v-for="related in searchResult.relatedEnterprises" :key="related.id" class="related-item">
                  <div class="related-info">
                    <h4>{{ related.name }}</h4>
                    <p>{{ related.relationship }}</p>
                  </div>
                  <div class="related-meta">
                    <span>持股比例：{{ related.shareRatio }}</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="经营异常" name="abnormal">
              <div class="abnormal-records">
                <div v-if="searchResult.abnormalRecords.length === 0" class="no-data">
                  <el-icon><SuccessFilled /></el-icon>
                  <p>暂无经营异常记录</p>
                </div>
                <div v-else>
                  <div v-for="record in searchResult.abnormalRecords" :key="record.id" class="abnormal-item">
                    <div class="abnormal-header">
                      <span class="abnormal-reason">{{ record.reason }}</span>
                      <span class="abnormal-date">{{ record.date }}</span>
                    </div>
                    <p class="abnormal-desc">{{ record.description }}</p>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>

    <div v-if="!searchResult && !isSearching" class="empty-state">
      <el-icon class="empty-icon"><Search /></el-icon>
      <h3>企业信息查询</h3>
      <p>输入企业名称或统一社会信用代码开始查询</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const searchKeyword = ref('')
const isSearching = ref(false)
const searchResult = ref<any>(null)
const activeTab = ref('basic')

// 模拟企业查询
const searchEnterprise = async () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入查询关键词')
    return
  }

  isSearching.value = true
  
  // 模拟API调用
  setTimeout(() => {
    searchResult.value = {
      name: '北京科技创新有限公司',
      creditCode: '91110000123456789X',
      legalPerson: '张三',
      registeredCapital: '1000万人民币',
      establishDate: '2018-03-15',
      businessStatus: '存续',
      status: '正常',
      type: '有限责任公司',
      industry: '软件和信息技术服务业',
      address: '北京市海淀区中关村大街1号',
      businessScope: '技术开发、技术推广、技术转让、技术咨询、技术服务；销售自行开发的产品；计算机系统服务；基础软件服务；应用软件服务。',
      riskScore: 85,
      risks: [
        {
          type: 'legal',
          title: '法律诉讼',
          level: 'low',
          count: 2,
          description: '作为被告参与2起合同纠纷案件，均已结案'
        },
        {
          type: 'finance',
          title: '财务风险',
          level: 'low',
          count: 1,
          description: '近期无重大财务异常'
        },
        {
          type: 'business',
          title: '经营风险',
          level: 'medium',
          count: 3,
          description: '存在3项行政处罚记录'
        }
      ],
      relatedEnterprises: [
        {
          id: '1',
          name: '北京科技投资有限公司',
          relationship: '控股股东',
          shareRatio: '60%'
        },
        {
          id: '2',
          name: '创新科技（上海）有限公司',
          relationship: '全资子公司',
          shareRatio: '100%'
        }
      ],
      abnormalRecords: []
    }
    isSearching.value = false
    ElMessage.success('查询成功')
  }, 2000)
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '正常': 'success',
    '异常': 'danger',
    '注销': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取风险等级
const getRiskLevel = (score: number) => {
  if (score >= 80) return 'low-risk'
  if (score >= 60) return 'medium-risk'
  return 'high-risk'
}

// 获取风险等级文本
const getRiskLevelText = (score: number) => {
  if (score >= 80) return '低风险'
  if (score >= 60) return '中风险'
  return '高风险'
}

// 生成尽调报告
const generateReport = () => {
  ElMessage.info('正在生成尽职调查报告，请稍候...')
}

// 开启监测
const startMonitoring = () => {
  ElMessage.success('已开启企业动态监测')
}
</script>

<style lang="scss" scoped>
.enterprise-info-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .search-section {
    margin-bottom: 30px;
    
    .search-card {
      background: #fff;
      padding: 30px;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      text-align: center;
      
      h2 {
        margin: 0 0 20px 0;
        color: #303133;
        font-size: 18px;
      }
      
      .search-form {
        max-width: 600px;
        margin: 0 auto;
      }
    }
  }

  .result-section {
    .enterprise-card {
      background: #fff;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      overflow: hidden;
      
      .enterprise-header {
        padding: 24px;
        border-bottom: 1px solid #e4e7ed;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        
        .enterprise-basic {
          h2 {
            margin: 0 0 12px 0;
            color: #303133;
            font-size: 20px;
          }
          
          .enterprise-tags {
            display: flex;
            gap: 8px;
          }
        }
        
        .enterprise-actions {
          display: flex;
          gap: 12px;
        }
      }
      
      .enterprise-content {
        padding: 24px;
        
        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
          
          .info-item {
            display: flex;
            
            &.full-width {
              grid-column: 1 / -1;
            }
            
            label {
              min-width: 120px;
              color: #909399;
              font-weight: 500;
            }
            
            span {
              color: #303133;
              flex: 1;
            }
          }
        }
        
        .risk-overview {
          .risk-score {
            display: flex;
            align-items: center;
            gap: 24px;
            margin-bottom: 24px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
            
            .score-circle {
              width: 80px;
              height: 80px;
              border-radius: 50%;
              background: #409eff;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              color: #fff;
              
              .score-number {
                font-size: 24px;
                font-weight: 600;
              }
              
              .score-label {
                font-size: 12px;
              }
            }
            
            .score-desc {
              .low-risk { color: #67c23a; }
              .medium-risk { color: #e6a23c; }
              .high-risk { color: #f56c6c; }
            }
          }
          
          .risk-items {
            .risk-item {
              padding: 16px;
              border: 1px solid #e4e7ed;
              border-radius: 6px;
              margin-bottom: 12px;
              
              .risk-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                
                .risk-title {
                  font-weight: 500;
                  color: #303133;
                }
              }
              
              .risk-desc {
                margin: 0;
                color: #606266;
                font-size: 14px;
              }
            }
          }
        }
        
        .related-enterprises {
          .related-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            margin-bottom: 12px;
            
            .related-info {
              h4 {
                margin: 0 0 4px 0;
                color: #303133;
                font-size: 16px;
              }
              
              p {
                margin: 0;
                color: #606266;
                font-size: 14px;
              }
            }
            
            .related-meta {
              color: #909399;
              font-size: 14px;
            }
          }
        }
        
        .abnormal-records {
          .no-data {
            text-align: center;
            padding: 40px;
            color: #67c23a;
            
            .el-icon {
              font-size: 48px;
              margin-bottom: 16px;
            }
            
            p {
              margin: 0;
              font-size: 16px;
            }
          }
          
          .abnormal-item {
            padding: 16px;
            border: 1px solid #f56c6c;
            border-radius: 6px;
            margin-bottom: 12px;
            background: #fef0f0;
            
            .abnormal-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;
              
              .abnormal-reason {
                font-weight: 500;
                color: #f56c6c;
              }
              
              .abnormal-date {
                color: #909399;
                font-size: 14px;
              }
            }
            
            .abnormal-desc {
              margin: 0;
              color: #606266;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #909399;
    
    .empty-icon {
      font-size: 64px;
      margin-bottom: 16px;
    }
    
    h3 {
      margin: 0 0 8px 0;
      color: #606266;
    }
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

@media (max-width: 768px) {
  .enterprise-info-page {
    padding: 15px;
    
    .result-section .enterprise-card {
      .enterprise-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }
      
      .enterprise-content .info-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
