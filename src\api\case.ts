import request from '@/utils/request'
import type { Case } from '@/types'
import { mockCaseAPI } from '@/mock/api'

// 是否启用Mock模式
const ENABLE_MOCK = import.meta.env.VITE_ENABLE_MOCK !== 'false'

// 获取案件列表
export const getCaseList = (params: any) => {
  if (ENABLE_MOCK) {
    return mockCaseAPI.getList(params)
  }
  return request({
    url: '/case/list',
    method: 'get',
    params
  })
}

// 获取案件详情
export const getCaseDetail = (id: string) => {
  if (ENABLE_MOCK) {
    return mockCaseAPI.getDetail(id)
  }
  return request({
    url: `/case/${id}`,
    method: 'get'
  })
}

// 创建案件
export const createCase = (data: Partial<Case>) => {
  if (ENABLE_MOCK) {
    return mockCaseAPI.create(data)
  }
  return request({
    url: '/case',
    method: 'post',
    data
  })
}

// 更新案件
export const updateCase = (id: string, data: Partial<Case>) => {
  if (ENABLE_MOCK) {
    return mockCaseAPI.update(id, data)
  }
  return request({
    url: `/case/${id}`,
    method: 'put',
    data
  })
}

// 删除案件
export const deleteCase = (id: string) => {
  if (ENABLE_MOCK) {
    return mockCaseAPI.delete(id)
  }
  return request({
    url: `/case/${id}`,
    method: 'delete'
  })
}

// 更新案件状态
export const updateCaseStatus = (id: string, status: string) => {
  if (ENABLE_MOCK) {
    return mockCaseAPI.update(id, { status })
  }
  return request({
    url: `/case/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 分配案件
export const assignCase = (id: string, lawyerId: string) => {
  if (ENABLE_MOCK) {
    return mockCaseAPI.update(id, { lawyerId })
  }
  return request({
    url: `/case/${id}/assign`,
    method: 'put',
    data: { lawyerId }
  })
}

// 获取案件统计
export const getCaseStats = () => {
  if (ENABLE_MOCK) {
    return Promise.resolve({
      code: 200,
      data: {
        total: 10,
        pending: 3,
        processing: 5,
        completed: 2,
        urgent: 2,
        high: 2,
        medium: 4,
        low: 2
      },
      message: '获取成功'
    })
  }
  return request({
    url: '/case/stats',
    method: 'get'
  })
}
