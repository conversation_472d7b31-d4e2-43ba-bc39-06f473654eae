import request from '@/utils/request'
import type { Client } from '@/types'
import { mockClientAPI } from '@/mock/api'

// 是否启用Mock模式
const ENABLE_MOCK = import.meta.env.VITE_ENABLE_MOCK !== 'false'

// 获取客户列表
export const getClientList = (params: any) => {
  if (ENABLE_MOCK) {
    return mockClientAPI.getList(params)
  }
  return request({
    url: '/client/page',
    method: 'get',
    params
  })
}

// 获取客户详情
export const getClientDetail = (id: string) => {
  if (ENABLE_MOCK) {
    return mockClientAPI.getDetail(id)
  }
  return request({
    url: `/client/${id}`,
    method: 'get'
  })
}

// 创建客户
export const createClient = (data: Partial<Client>) => {
  if (ENABLE_MOCK) {
    return mockClientAPI.create(data)
  }
  return request({
    url: '/client',
    method: 'post',
    data
  })
}

// 更新客户
export const updateClient = (id: string, data: Partial<Client>) => {
  if (ENABLE_MOCK) {
    return mockClientAPI.update(id, data)
  }
  return request({
    url: `/client/${id}`,
    method: 'put',
    data
  })
}

// 删除客户
export const deleteClient = (id: string) => {
  if (ENABLE_MOCK) {
    return mockClientAPI.delete(id)
  }
  return request({
    url: `/client/${id}`,
    method: 'delete'
  })
}

// 获取客户统计
export const getClientStats = () => {
  if (ENABLE_MOCK) {
    return Promise.resolve({
      code: 200,
      data: {
        total: 15,
        active: 14,
        inactive: 1,
        companies: 10,
        individuals: 4,
        organizations: 1
      },
      message: '获取成功'
    })
  }
  return request({
    url: '/client/stats',
    method: 'get'
  })
}
