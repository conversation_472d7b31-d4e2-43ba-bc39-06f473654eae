package com.cloudlegal.service;

import com.cloudlegal.dto.LoginRequest;
import com.cloudlegal.dto.LoginResponse;

/**
 * 认证服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface AuthService {

    /**
     * 用户登录
     * 
     * @param loginRequest 登录请求
     * @param clientIp 客户端IP
     * @return 登录响应
     */
    LoginResponse login(LoginRequest loginRequest, String clientIp);

    /**
     * 用户登出
     * 
     * @param token JWT Token
     */
    void logout(String token);

    /**
     * 刷新Token
     * 
     * @param refreshToken 刷新Token
     * @return 新的登录响应
     */
    LoginResponse refreshToken(String refreshToken);

    /**
     * 获取当前用户信息
     * 
     * @param token JWT Token
     * @return 用户信息
     */
    Object getCurrentUser(String token);

    /**
     * 验证Token
     * 
     * @param token JWT Token
     * @return 是否有效
     */
    boolean validateToken(String token);

    /**
     * 从Token中获取用户ID
     * 
     * @param token JWT Token
     * @return 用户ID
     */
    Long getUserIdFromToken(String token);

    /**
     * 检查用户权限
     * 
     * @param userId 用户ID
     * @param permission 权限标识
     * @return 是否有权限
     */
    boolean hasPermission(Long userId, String permission);
}
