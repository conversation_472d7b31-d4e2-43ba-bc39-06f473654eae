<template>
  <div class="client-detail">
    <div class="page-header">
      <el-page-header @back="handleBack" title="客户详情">
        <template #content>
          <span class="text-large font-600 mr-3">{{ clientData?.clientName }}</span>
          <el-tag v-if="clientData" :type="getClientTypeTag(clientData.clientType)">
            {{ getClientTypeText(clientData.clientType) }}
          </el-tag>
        </template>
        <template #extra>
          <el-button type="primary" @click="handleEdit">编辑客户</el-button>
        </template>
      </el-page-header>
    </div>

    <div class="page-content" v-loading="loading">
      <!-- 基本信息 -->
      <el-card class="mb-4">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
          </div>
        </template>
        
        <el-descriptions v-if="clientData" :column="3" border>
          <el-descriptions-item label="客户名称" :span="2">{{ clientData.clientName }}</el-descriptions-item>
          <el-descriptions-item label="客户类型">
            <el-tag :type="getClientTypeTag(clientData.clientType)">
              {{ getClientTypeText(clientData.clientType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="联系人">{{ clientData.contactPerson }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ clientData.phone }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ clientData.email }}</el-descriptions-item>
          <el-descriptions-item label="行业" v-if="clientData.industry">{{ clientData.industry }}</el-descriptions-item>
          <el-descriptions-item label="地址" :span="clientData.industry ? 2 : 3">{{ clientData.address }}</el-descriptions-item>
          
          <!-- 企业特有信息 -->
          <template v-if="clientData.clientType === 2">
            <el-descriptions-item label="统一社会信用代码" v-if="clientData.creditCode" :span="2">
              {{ clientData.creditCode }}
            </el-descriptions-item>
            <el-descriptions-item label="法定代表人" v-if="clientData.legalPerson">
              {{ clientData.legalPerson }}
            </el-descriptions-item>
            <el-descriptions-item label="注册资本" v-if="clientData.registeredCapital" :span="2">
              {{ formatMoney(clientData.registeredCapital) }}
            </el-descriptions-item>
          </template>
          
          <el-descriptions-item label="状态">
            <el-tag :type="clientData.status === 1 ? 'success' : 'danger'">
              {{ clientData.status === 1 ? '正常' : '停用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(clientData.createdTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(clientData.updatedTime) }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="3" v-if="clientData.description">
            {{ clientData.description }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 相关案件 -->
      <el-card class="mb-4" v-if="relatedCases.length > 0">
        <template #header>
          <div class="card-header">
            <span>相关案件 ({{ relatedCases.length }})</span>
            <el-button type="primary" size="small" @click="handleAddCase">新增案件</el-button>
          </div>
        </template>
        
        <el-table :data="relatedCases" stripe>
          <el-table-column prop="caseNumber" label="案件编号" width="140" />
          <el-table-column prop="caseTitle" label="案件标题" show-overflow-tooltip />
          <el-table-column prop="caseType" label="案件类型" width="120" />
          <el-table-column prop="caseStatus" label="状态" width="100">
            <template #default="{ row }">
              <el-tag size="small" :type="getCaseStatusTag(row.caseStatus)">
                {{ getCaseStatusText(row.caseStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="caseAmount" label="金额" width="120">
            <template #default="{ row }">
              {{ row.caseAmount ? formatMoney(row.caseAmount) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="lawyerName" label="负责律师" width="100" />
          <el-table-column prop="createdTime" label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.createdTime, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleViewCase(row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 相关合同 -->
      <el-card v-if="relatedContracts.length > 0">
        <template #header>
          <div class="card-header">
            <span>相关合同 ({{ relatedContracts.length }})</span>
            <el-button type="primary" size="small" @click="handleAddContract">新增合同</el-button>
          </div>
        </template>
        
        <el-table :data="relatedContracts" stripe>
          <el-table-column prop="contractNumber" label="合同编号" width="140" />
          <el-table-column prop="contractTitle" label="合同标题" show-overflow-tooltip />
          <el-table-column prop="contractType" label="合同类型" width="120" />
          <el-table-column prop="contractStatus" label="状态" width="100">
            <template #default="{ row }">
              <el-tag size="small" :type="getContractStatusTag(row.contractStatus)">
                {{ getContractStatusText(row.contractStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="contractAmount" label="金额" width="120">
            <template #default="{ row }">
              {{ row.contractAmount ? formatMoney(row.contractAmount) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="signDate" label="签署日期" width="120">
            <template #default="{ row }">
              {{ row.signDate ? formatDate(row.signDate, 'YYYY-MM-DD') : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="createdTime" label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.createdTime, 'YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleViewContract(row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 空状态 -->
      <el-empty v-if="!relatedCases.length && !relatedContracts.length" description="暂无相关案件和合同">
        <el-button type="primary" @click="handleAddCase">新增案件</el-button>
        <el-button @click="handleAddContract">新增合同</el-button>
      </el-empty>
    </div>

    <!-- 编辑客户对话框 -->
    <ClientForm
      v-model:visible="editVisible"
      :form-data="clientData"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getClientDetail } from '@/api/client'
import { formatDate, formatMoney } from '@/utils'
import type { Client } from '@/types'
import ClientForm from './components/ClientForm.vue'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const editVisible = ref(false)
const clientData = ref<Client | null>(null)
const relatedCases = ref<any[]>([])
const relatedContracts = ref<any[]>([])

const clientId = route.params.id as string

// 获取客户类型文本
const getClientTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '个人',
    2: '企业',
    3: '组织'
  }
  return typeMap[type] || '未知'
}

// 获取客户类型标签样式
const getClientTypeTag = (type: number) => {
  const tagMap: Record<number, string> = {
    1: 'primary',
    2: 'success',
    3: 'warning'
  }
  return tagMap[type] || 'info'
}

// 获取案件状态文本
const getCaseStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'closed': '已关闭'
  }
  return statusMap[status] || status
}

// 获取案件状态标签样式
const getCaseStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    'pending': 'warning',
    'processing': 'primary',
    'completed': 'success',
    'closed': 'info'
  }
  return tagMap[status] || 'info'
}

// 获取合同状态文本
const getContractStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'draft': '草稿',
    'reviewing': '审核中',
    'approved': '已批准',
    'signed': '已签署',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

// 获取合同状态标签样式
const getContractStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    'draft': 'info',
    'reviewing': 'warning',
    'approved': 'primary',
    'signed': 'success',
    'expired': 'danger'
  }
  return tagMap[status] || 'info'
}

// 获取客户详情
const fetchClientDetail = async () => {
  try {
    loading.value = true
    const response = await getClientDetail(clientId)
    clientData.value = response.data
    relatedCases.value = response.relatedCases || []
    relatedContracts.value = response.relatedContracts || []
  } catch (error) {
    ElMessage.error('获取客户详情失败')
    router.back()
  } finally {
    loading.value = false
  }
}

// 返回
const handleBack = () => {
  router.back()
}

// 编辑客户
const handleEdit = () => {
  editVisible.value = true
}

// 编辑成功
const handleEditSuccess = () => {
  fetchClientDetail()
}

// 新增案件
const handleAddCase = () => {
  router.push(`/cases/create?clientId=${clientId}`)
}

// 查看案件
const handleViewCase = (row: any) => {
  router.push(`/cases/${row.id}`)
}

// 新增合同
const handleAddContract = () => {
  router.push(`/contracts/create?clientId=${clientId}`)
}

// 查看合同
const handleViewContract = (row: any) => {
  router.push(`/contracts/${row.id}`)
}

onMounted(() => {
  fetchClientDetail()
})
</script>

<style lang="scss" scoped>
.client-detail {
  .page-header {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .mb-4 {
    margin-bottom: 16px;
  }
}
</style>
