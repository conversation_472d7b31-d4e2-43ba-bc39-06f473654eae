/**
 * 代理配置测试脚本
 * 用于测试前端代理配置是否正确
 */

const axios = require('axios')

// 测试配置
const config = {
  frontend: 'http://localhost:8090',
  backend: 'http://localhost:8080',
  testEndpoints: [
    '/api/auth/login',
    '/api/user/info',
    '/api/client/list',
    '/api/case/list',
    '/api/contract/list'
  ]
}

async function testProxy() {
  console.log('🚀 开始测试代理配置...\n')
  
  // 测试前端服务是否启动
  try {
    await axios.get(config.frontend)
    console.log('✅ 前端服务正常运行')
  } catch (error) {
    console.log('❌ 前端服务未启动，请先运行 npm run dev')
    return
  }
  
  // 测试后端服务是否启动
  try {
    await axios.get(`${config.backend}/api/health`, { timeout: 3000 })
    console.log('✅ 后端服务正常运行')
  } catch (error) {
    console.log('⚠️  后端服务可能未启动或健康检查接口不存在')
  }
  
  console.log('\n📡 测试代理转发...')
  
  // 测试代理转发
  for (const endpoint of config.testEndpoints) {
    try {
      const response = await axios.get(`${config.frontend}${endpoint}`, {
        timeout: 5000,
        validateStatus: () => true // 接受所有状态码
      })
      
      if (response.status === 404) {
        console.log(`⚠️  ${endpoint} - 接口不存在 (404)`)
      } else if (response.status === 401) {
        console.log(`✅ ${endpoint} - 代理正常，需要认证 (401)`)
      } else if (response.status >= 200 && response.status < 300) {
        console.log(`✅ ${endpoint} - 代理正常 (${response.status})`)
      } else {
        console.log(`⚠️  ${endpoint} - 状态码: ${response.status}`)
      }
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`❌ ${endpoint} - 连接被拒绝，检查后端服务`)
      } else {
        console.log(`❌ ${endpoint} - 错误: ${error.message}`)
      }
    }
  }
  
  console.log('\n📋 配置检查完成!')
  console.log('如果看到连接被拒绝的错误，请确保后端服务在 localhost:8080 上运行')
  console.log('如果看到 401 错误，说明代理配置正确，只是需要登录认证')
}

// 运行测试
testProxy().catch(console.error)
