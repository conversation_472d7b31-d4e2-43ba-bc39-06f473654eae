// 调试工具函数

export const debugRouter = (router: any) => {
  console.log('🔍 路由调试信息:')
  console.log('所有路由:', router.getRoutes())
  
  const mainRoute = router.getRoutes().find((r: any) => r.path === '/')
  console.log('主路由:', mainRoute)
  console.log('子路由:', mainRoute?.children)
}

export const debugMenu = (menuRoutes: any[]) => {
  console.log('🔍 菜单调试信息:')
  console.log('菜单路由:', menuRoutes)
  menuRoutes.forEach((route, index) => {
    console.log(`菜单项 ${index}:`, {
      path: route.path,
      name: route.name,
      title: route.meta?.title,
      icon: route.meta?.icon,
      hidden: route.meta?.hidden
    })
  })
}

export const debugRoute = (route: any) => {
  console.log('🔍 当前路由信息:')
  console.log('路径:', route.path)
  console.log('名称:', route.name)
  console.log('参数:', route.params)
  console.log('查询:', route.query)
  console.log('匹配的路由:', route.matched)
}
