# 云法务系统产品文档

## 📋 文档概述

**文档版本**: v1.0.0  
**创建日期**: 2024-01-01  
**最后更新**: 2024-01-01  
**产品名称**: 云法务管理系统  
**产品版本**: v1.0.0  

## 🎯 产品概述

### 产品定位
云法务系统是一款面向律师事务所、企业法务部门和法律服务机构的综合性法务管理平台，通过数字化手段提升法务工作效率，降低管理成本，提供专业的法律服务支持。

### 核心价值
- **效率提升**: 通过智能化工具减少重复性工作，提升工作效率50%以上
- **风险控制**: 全面的风险识别和预警机制，降低法律风险
- **成本优化**: 透明的费用管理和自动化流程，降低运营成本30%
- **服务标准化**: 统一的服务流程和质量标准，提升客户满意度

### 目标用户
- **律师事务所**: 中小型律师事务所，需要专业的案件和客户管理
- **企业法务**: 中大型企业法务部门，需要合规管理和风险控制
- **法律服务机构**: 提供法律咨询和服务的专业机构
- **个人律师**: 独立执业律师，需要个人业务管理工具

## 🏗️ 系统架构

### 技术架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        用户层                                │
├─────────────────────────────────────────────────────────────┤
│  Web浏览器  │  移动端APP  │  微信小程序  │  第三方系统集成    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      前端层                                  │
├─────────────────────────────────────────────────────────────┤
│  Vue 3 + TypeScript + Element Plus + Vite                  │
│  • 响应式设计  • 组件化开发  • 状态管理  • 路由管理         │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      网关层                                  │
├─────────────────────────────────────────────────────────────┤
│  Nginx / Spring Cloud Gateway                              │
│  • 负载均衡  • 反向代理  • SSL终端  • 限流熔断             │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      应用层                                  │
├─────────────────────────────────────────────────────────────┤
│  Spring Boot 3.x + Spring Security + JWT                   │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │  用户管理   │  客户管理   │  案件管理   │  合同管理   │   │
│  ├─────────────┼─────────────┼─────────────┼─────────────┤   │
│  │  文档管理   │  财务管理   │  智能服务   │  系统管理   │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      数据层                                  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│  │   MySQL     │    Redis    │   MinIO     │ Elasticsearch│   │
│  │  主数据库   │   缓存层    │  文件存储   │   搜索引擎   │   │
│  └─────────────┴─────────────┴─────────────┴─────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 业务架构图
```
┌─────────────────────────────────────────────────────────────┐
│                      云法务系统                              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────┐   │
│  │                  核心业务层                          │   │
│  │  ┌─────────────┬─────────────┬─────────────────────┐ │   │
│  │  │  客户关系   │  案件管理   │    合同管理         │ │   │
│  │  │    管理     │             │                     │ │   │
│  │  ├─────────────┼─────────────┼─────────────────────┤ │   │
│  │  │  文档管理   │  财务管理   │    工作流管理       │ │   │
│  │  └─────────────┴─────────────┴─────────────────────┘ │   │
│  └─────────────────────────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                  智能服务层                          │   │
│  │  ┌─────────────┬─────────────┬─────────────────────┐ │   │
│  │  │  智能咨询   │  智能文书   │    法律检索         │ │   │
│  │  ├─────────────┼─────────────┼─────────────────────┤ │   │
│  │  │  企业信息   │法律计算器   │    法务服务         │ │   │
│  │  └─────────────┴─────────────┴─────────────────────┘ │   │
│  └─────────────────────────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                  基础服务层                          │   │
│  │  ┌─────────────┬─────────────┬─────────────────────┐ │   │
│  │  │  用户管理   │  权限管理   │    系统配置         │ │   │
│  │  ├─────────────┼─────────────┼─────────────────────┤ │   │
│  │  │  日志管理   │  消息通知   │    数据备份         │ │   │
│  │  └─────────────┴─────────────┴─────────────────────┘ │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 🔐 用户权限体系

### 角色权限矩阵

| 功能模块 | 超级管理员 | 管理员 | 律师 | 助理 | 客户 |
|---------|-----------|--------|------|------|------|
| 用户管理 | ✅ | ✅ | ❌ | ❌ | ❌ |
| 角色权限 | ✅ | ✅ | ❌ | ❌ | ❌ |
| 客户管理 | ✅ | ✅ | ✅ | ✅ | 👁️ |
| 案件管理 | ✅ | ✅ | ✅ | ✅ | 👁️ |
| 合同管理 | ✅ | ✅ | ✅ | ✅ | 👁️ |
| 文档管理 | ✅ | ✅ | ✅ | ✅ | 👁️ |
| 财务管理 | ✅ | ✅ | ✅ | ❌ | ❌ |
| 智能服务 | ✅ | ✅ | ✅ | ✅ | ✅ |
| 系统配置 | ✅ | ✅ | ❌ | ❌ | ❌ |
| 数据统计 | ✅ | ✅ | ✅ | 👁️ | ❌ |

**图例**: ✅ 完全权限 | 👁️ 只读权限 | ❌ 无权限

### 权限控制流程图
```mermaid
graph TD
    A[用户登录] --> B{验证用户名密码}
    B -->|成功| C[生成JWT Token]
    B -->|失败| D[返回登录失败]
    C --> E[获取用户角色]
    E --> F[加载角色权限]
    F --> G[访问系统功能]
    G --> H{检查功能权限}
    H -->|有权限| I[执行操作]
    H -->|无权限| J[返回权限不足]
    I --> K[记录操作日志]
    K --> L[返回操作结果]
```

## 📊 核心业务流程

### 1. 客户管理流程

#### 客户生命周期管理
```mermaid
graph TD
    A[潜在客户] --> B[客户咨询]
    B --> C{初步评估}
    C -->|符合条件| D[创建客户档案]
    C -->|不符合| E[记录咨询信息]
    D --> F[签署服务协议]
    F --> G[正式客户]
    G --> H[提供法律服务]
    H --> I[服务完成]
    I --> J{客户满意度}
    J -->|满意| K[维护客户关系]
    J -->|不满意| L[改进服务质量]
    K --> M[长期合作客户]
    L --> H
```

#### 客户信息管理规则

**客户类型判定规则**:
```
IF 客户类型 == "企业" THEN
    必填字段: [企业名称, 统一社会信用代码, 法定代表人, 联系人, 联系电话]
    可选字段: [注册资本, 所属行业, 企业地址, 邮箱]
ELSE IF 客户类型 == "个人" THEN
    必填字段: [姓名, 联系电话]
    可选字段: [身份证号, 邮箱, 地址]
ELSE IF 客户类型 == "组织" THEN
    必填字段: [组织名称, 负责人, 联系电话]
    可选字段: [组织代码, 地址, 邮箱]
```

**客户状态管理规则**:
```
客户状态转换规则:
- 新建 → 正常: 完善基本信息后自动转换
- 正常 → 暂停: 手动设置，停止提供服务
- 暂停 → 正常: 手动恢复，重新提供服务
- 正常 → 注销: 服务结束，保留历史记录
```

### 2. 案件管理流程

#### 案件全生命周期流程
```mermaid
graph TD
    A[案件咨询] --> B[案件评估]
    B --> C{是否接受}
    C -->|接受| D[创建案件]
    C -->|拒绝| E[记录拒绝原因]
    D --> F[分配律师]
    F --> G[制定案件策略]
    G --> H[案件执行]
    H --> I{案件状态检查}
    I -->|进行中| J[更新案件进度]
    I -->|暂停| K[记录暂停原因]
    I -->|完成| L[案件结案]
    J --> H
    K --> M{是否恢复}
    M -->|是| H
    M -->|否| N[案件终止]
    L --> O[客户满意度调查]
    O --> P[归档案件资料]
```

#### 案件管理规则

**案件类型分类规则**:
```
案件类型映射:
- civil: 民事纠纷 (合同纠纷、侵权纠纷、婚姻家庭等)
- criminal: 刑事案件 (刑事辩护、取保候审等)
- administrative: 行政案件 (行政复议、行政诉讼等)
- corporate: 公司法务 (股权纠纷、公司治理等)
- intellectual: 知识产权 (专利、商标、著作权等)
- labor: 劳动争议 (劳动合同、工伤赔偿等)
```

**案件状态流转规则**:
```
状态流转规则:
pending → processing: 分配律师后自动转换
processing → suspended: 手动暂停，需要填写暂停原因
suspended → processing: 手动恢复，需要填写恢复原因
processing → completed: 案件结案，需要填写结案报告
completed → archived: 30天后自动归档
```

**优先级判定规则**:
```
优先级自动判定:
IF 案件金额 >= 1000000 OR 客户类型 == "重要客户" THEN
    优先级 = "urgent"
ELSE IF 案件金额 >= 500000 OR 涉及刑事案件 THEN
    优先级 = "high"
ELSE IF 案件金额 >= 100000 THEN
    优先级 = "medium"
ELSE
    优先级 = "low"
```

### 3. 合同管理流程

#### 合同生命周期管理
```mermaid
graph TD
    A[合同需求] --> B[合同起草]
    B --> C[内部审查]
    C --> D{审查结果}
    D -->|通过| E[发送客户]
    D -->|需修改| F[修改合同]
    F --> C
    E --> G[客户审查]
    G --> H{客户反馈}
    H -->|同意| I[签署合同]
    H -->|需修改| J[协商修改]
    J --> B
    I --> K[合同生效]
    K --> L[履行监控]
    L --> M{履行状态}
    M -->|正常履行| N[合同完成]
    M -->|违约| O[违约处理]
    O --> P[争议解决]
    P --> Q[合同终止]
    N --> R[合同归档]
    Q --> R
```

#### 合同管理规则

**合同类型分类规则**:
```
合同类型定义:
- service: 服务合同 (法律服务、咨询服务等)
- sales: 买卖合同 (商品买卖、设备采购等)
- lease: 租赁合同 (房屋租赁、设备租赁等)
- employment: 劳动合同 (员工合同、顾问合同等)
- partnership: 合作协议 (战略合作、业务合作等)
- license: 许可协议 (知识产权许可、软件许可等)
- consulting: 咨询合同 (管理咨询、技术咨询等)
```

**合同状态管理规则**:
```
状态流转规则:
draft → reviewing: 提交审查时自动转换
reviewing → approved: 审查通过后转换
reviewing → rejected: 审查不通过，需填写拒绝原因
approved → signed: 双方签署后转换
signed → effective: 合同生效日期到达后自动转换
effective → completed: 合同履行完成后转换
effective → terminated: 合同提前终止时转换
```

**合同到期提醒规则**:
```
提醒规则:
- 合同到期前30天: 发送第一次提醒
- 合同到期前15天: 发送第二次提醒
- 合同到期前7天: 发送紧急提醒
- 合同到期当天: 发送到期通知
```

### 4. 财务管理流程

#### 财务流程图
```mermaid
graph TD
    A[业务发生] --> B{收入/支出}
    B -->|收入| C[开具发票]
    B -->|支出| D[费用申请]
    C --> E[收款确认]
    D --> F[费用审批]
    E --> G[记录收入]
    F --> H{审批结果}
    H -->|通过| I[记录支出]
    H -->|拒绝| J[驳回申请]
    G --> K[更新财务报表]
    I --> K
    K --> L[生成财务报告]
    L --> M[财务分析]
```

#### 财务管理规则

**收支分类规则**:
```
收入分类:
- lawyer_fee: 律师费 (案件代理费、法律咨询费)
- consulting_fee: 咨询费 (专项咨询、顾问费)
- training_fee: 培训费 (法律培训、讲座费)
- other_income: 其他收入 (利息收入、投资收益等)

支出分类:
- office_expense: 办公费用 (租金、水电、办公用品)
- travel_expense: 差旅费 (交通费、住宿费、餐费)
- court_fee: 诉讼费 (案件受理费、公告费等)
- marketing_expense: 营销费用 (广告费、推广费)
- other_expense: 其他支出 (培训费、会议费等)
```

**财务审批规则**:
```
审批权限规则:
IF 金额 <= 1000 THEN
    审批人 = "部门主管"
ELSE IF 金额 <= 5000 THEN
    审批人 = "财务经理"
ELSE IF 金额 <= 20000 THEN
    审批人 = "总经理"
ELSE
    审批人 = "董事会"
```

## 🤖 智能服务模块

### 1. 智能咨询服务

#### 咨询流程图
```mermaid
graph TD
    A[用户提问] --> B[问题预处理]
    B --> C[意图识别]
    C --> D{问题类型}
    D -->|法律条文| E[检索法律法规]
    D -->|案例咨询| F[检索相似案例]
    D -->|程序咨询| G[检索程序规定]
    D -->|其他| H[通用知识库]
    E --> I[生成回答]
    F --> I
    G --> I
    H --> I
    I --> J[回答质量评估]
    J --> K{质量检查}
    K -->|合格| L[返回答案]
    K -->|不合格| M[转人工客服]
    L --> N[用户反馈]
    N --> O[优化知识库]
```

#### 智能咨询规则

**问题分类规则**:
```
问题类型识别:
- 法律条文类: 包含"法律条文"、"法规"、"条例"等关键词
- 案例咨询类: 包含"案例"、"判决"、"类似情况"等关键词
- 程序咨询类: 包含"流程"、"程序"、"如何办理"等关键词
- 费用咨询类: 包含"费用"、"收费"、"多少钱"等关键词
```

**回答质量评估规则**:
```
质量评估标准:
- 相关性得分 >= 0.8: 高质量回答
- 相关性得分 >= 0.6: 中等质量回答
- 相关性得分 < 0.6: 低质量回答，转人工处理
```

### 2. 智能文书生成

#### 文书生成流程
```mermaid
graph TD
    A[选择文书类型] --> B[填写基本信息]
    B --> C[选择文书模板]
    C --> D[信息验证]
    D --> E{验证结果}
    E -->|通过| F[生成文书草稿]
    E -->|失败| G[提示错误信息]
    G --> B
    F --> H[文书预览]
    H --> I{用户确认}
    I -->|确认| J[生成最终文书]
    I -->|修改| K[编辑文书内容]
    K --> H
    J --> L[保存文书]
    L --> M[下载/打印]
```

#### 文书生成规则

**文书类型模板规则**:
```
文书模板映射:
- complaint: 起诉状模板 (民事起诉状、行政起诉状、刑事自诉状)
- answer: 答辩状模板 (民事答辩状、行政答辩状)
- appeal: 上诉状模板 (民事上诉状、刑事上诉状)
- lawyer-letter: 律师函模板 (催告函、警告函、协商函)
- contract: 合同模板 (服务合同、买卖合同、租赁合同)
- legal-opinion: 法律意见书模板 (尽职调查、合规审查)
```

**必填字段验证规则**:
```
起诉状必填字段:
- 原告信息: [姓名/名称, 地址, 联系方式]
- 被告信息: [姓名/名称, 地址]
- 诉讼请求: [具体请求内容]
- 事实与理由: [案件基本事实]

律师函必填字段:
- 委托人信息: [姓名/名称, 地址]
- 对方信息: [姓名/名称, 地址]
- 函件事由: [具体事项]
- 法律依据: [相关法条]
```

### 3. 法律计算器

#### 计算器类型和规则

**律师费计算规则**:
```
按标的额比例计算:
IF 标的额 <= 10000 THEN
    律师费 = MAX(标的额 * 8%, 1000)
ELSE IF 标的额 <= 100000 THEN
    律师费 = 1000 + (标的额 - 10000) * 6%
ELSE IF 标的额 <= 1000000 THEN
    律师费 = 6400 + (标的额 - 100000) * 4%
ELSE
    律师费 = 42400 + (标的额 - 1000000) * 2%

风险代理费 = 律师费 * 30%
总费用 = 律师费 + 风险代理费
```

**诉讼费计算规则**:
```
财产案件受理费:
IF 争议金额 <= 10000 THEN
    受理费 = 50
ELSE IF 争议金额 <= 100000 THEN
    受理费 = 50 + (争议金额 - 10000) * 2.5%
ELSE IF 争议金额 <= 200000 THEN
    受理费 = 2300 + (争议金额 - 100000) * 2%
ELSE
    受理费 = 4300 + (争议金额 - 200000) * 1.5%

非财产案件受理费 = 300
```

**利息计算规则**:
```
单利计算:
利息 = 本金 * 年利率 * (天数 / 365)

复利计算:
利息 = 本金 * (1 + 年利率)^(天数/365) - 本金
```

## 📈 数据统计与分析

### 统计指标体系

#### 客户统计指标
```
基础指标:
- 客户总数: COUNT(客户)
- 活跃客户数: COUNT(客户 WHERE 状态='正常')
- 新增客户数: COUNT(客户 WHERE 创建时间 >= 本月开始)
- 客户流失率: (本月流失客户数 / 上月客户总数) * 100%

分类指标:
- 企业客户占比: (企业客户数 / 客户总数) * 100%
- 个人客户占比: (个人客户数 / 客户总数) * 100%
- 行业分布: GROUP BY 所属行业
```

#### 案件统计指标
```
基础指标:
- 案件总数: COUNT(案件)
- 进行中案件: COUNT(案件 WHERE 状态='processing')
- 本月新增案件: COUNT(案件 WHERE 创建时间 >= 本月开始)
- 案件完成率: (已完成案件数 / 案件总数) * 100%

效率指标:
- 平均办案周期: AVG(结案时间 - 开始时间)
- 律师工作负荷: COUNT(案件) GROUP BY 律师
- 案件类型分布: COUNT(案件) GROUP BY 案件类型
```

#### 财务统计指标
```
收入指标:
- 总收入: SUM(收入记录.金额)
- 本月收入: SUM(收入记录.金额 WHERE 记录日期 >= 本月开始)
- 律师费收入: SUM(收入记录.金额 WHERE 分类='律师费')
- 收入增长率: ((本月收入 - 上月收入) / 上月收入) * 100%

支出指标:
- 总支出: SUM(支出记录.金额)
- 本月支出: SUM(支出记录.金额 WHERE 记录日期 >= 本月开始)
- 办公费用: SUM(支出记录.金额 WHERE 分类='办公费用')

盈利指标:
- 净利润: 总收入 - 总支出
- 利润率: (净利润 / 总收入) * 100%
```

### 数据分析规则

#### 异常数据检测规则
```
客户异常检测:
- 重复客户: 相同姓名 + 相同电话
- 无效客户: 创建超过30天但无任何业务记录

案件异常检测:
- 超期案件: 进行中状态超过预期完成时间30天
- 无进展案件: 30天内无任何更新记录

财务异常检测:
- 异常金额: 单笔金额超过历史平均值3倍
- 重复记录: 相同日期 + 相同金额 + 相同描述
```

#### 预警规则
```
客户预警:
- 重要客户流失预警: 重要客户30天无业务往来
- 客户满意度预警: 满意度评分 < 3分

案件预警:
- 案件延期预警: 距离预期完成时间不足7天
- 高风险案件预警: 案件金额 > 100万且优先级为紧急

财务预警:
- 现金流预警: 本月支出超过收入150%
- 应收账款预警: 应收账款账龄超过90天
```

## 🔄 业务流程自动化

### 工作流引擎

#### 案件审批流程
```mermaid
graph TD
    A[案件创建] --> B{案件金额判断}
    B -->|<10万| C[助理审核]
    B -->|10-50万| D[律师审核]
    B -->|>50万| E[合伙人审核]
    C --> F{审核结果}
    D --> F
    E --> F
    F -->|通过| G[案件立案]
    F -->|拒绝| H[退回修改]
    G --> I[分配律师]
    I --> J[案件执行]
    H --> A
```

#### 合同审批流程
```mermaid
graph TD
    A[合同起草] --> B[法务审查]
    B --> C{审查结果}
    C -->|通过| D[业务部门确认]
    C -->|需修改| E[修改合同]
    E --> B
    D --> F{确认结果}
    F -->|同意| G[领导审批]
    F -->|不同意| H[重新协商]
    G --> I{审批结果}
    I -->|通过| J[合同签署]
    I -->|拒绝| K[终止合同]
    H --> A
```

### 自动化规则

#### 任务自动分配规则
```
律师分配规则:
IF 案件类型 == "知识产权" THEN
    分配给专业律师 WHERE 专业领域 CONTAINS "知识产权"
ELSE IF 案件类型 == "刑事案件" THEN
    分配给专业律师 WHERE 专业领域 CONTAINS "刑事辩护"
ELSE
    分配给工作负荷最轻的律师
```

#### 提醒通知规则
```
自动提醒规则:
- 案件开庭提醒: 开庭前3天、1天、2小时
- 合同到期提醒: 到期前30天、15天、7天、当天
- 费用催收提醒: 逾期7天、15天、30天
- 文档归档提醒: 案件结案后7天
```

## 📱 系统集成接口

### 第三方系统集成

#### 法院系统对接
```
接口功能:
- 案件信息同步
- 开庭通知获取
- 判决书下载
- 执行信息查询

数据格式: JSON
认证方式: API Key + 数字签名
```

#### 企业信用查询
```
接口功能:
- 企业基本信息查询
- 企业风险信息获取
- 法人变更记录
- 经营异常信息

数据来源: 国家企业信用信息公示系统
更新频率: 每日同步
```

#### 银行支付接口
```
支持银行:
- 工商银行
- 建设银行
- 招商银行
- 支付宝
- 微信支付

功能支持:
- 在线支付
- 退款处理
- 对账文件下载
- 交易状态查询
```

## 🛡️ 安全与合规

### 数据安全规则

#### 数据分类分级
```
数据分类:
- 公开数据: 系统公告、法律法规
- 内部数据: 客户基本信息、案件基本信息
- 敏感数据: 客户隐私信息、案件详细内容
- 机密数据: 财务数据、商业机密

访问控制:
- 公开数据: 所有用户可访问
- 内部数据: 相关业务人员可访问
- 敏感数据: 授权人员可访问
- 机密数据: 高级管理人员可访问
```

#### 数据加密规则
```
加密策略:
- 传输加密: HTTPS/TLS 1.3
- 存储加密: AES-256
- 密码加密: BCrypt
- 敏感字段加密: RSA-2048

密钥管理:
- 密钥轮换周期: 90天
- 密钥存储: 硬件安全模块(HSM)
- 密钥备份: 异地备份
```

### 合规要求

#### 法律法规遵循
```
适用法规:
- 《网络安全法》
- 《数据安全法》
- 《个人信息保护法》
- 《律师法》
- 《律师执业管理办法》

合规检查:
- 数据处理合规性检查
- 用户权限合规性检查
- 操作日志合规性检查
- 数据备份合规性检查
```

#### 审计要求
```
审计内容:
- 用户登录审计
- 数据访问审计
- 权限变更审计
- 系统配置审计

审计日志保存期限: 3年
审计报告生成周期: 月度、季度、年度
```

## 📋 系统运维

### 监控指标

#### 系统性能监控
```
监控指标:
- CPU使用率: 阈值 < 80%
- 内存使用率: 阈值 < 85%
- 磁盘使用率: 阈值 < 90%
- 网络带宽: 阈值 < 80%

应用监控:
- 响应时间: 阈值 < 2秒
- 错误率: 阈值 < 1%
- 并发用户数: 实时监控
- 数据库连接数: 阈值 < 80%
```

#### 业务监控
```
业务指标:
- 用户活跃度: 日活跃用户数
- 功能使用率: 各功能模块使用频率
- 数据增长率: 数据量增长趋势
- 系统可用性: 目标 99.9%
```

### 备份策略

#### 数据备份规则
```
备份策略:
- 全量备份: 每周日凌晨2点
- 增量备份: 每日凌晨2点
- 日志备份: 实时备份
- 配置备份: 变更时备份

备份保存:
- 本地备份: 保存30天
- 异地备份: 保存1年
- 归档备份: 永久保存
```

#### 灾难恢复
```
恢复策略:
- RTO(恢复时间目标): 4小时
- RPO(恢复点目标): 1小时
- 备用系统: 热备份
- 数据同步: 实时同步

恢复流程:
1. 故障检测与报警
2. 启动应急响应
3. 切换到备用系统
4. 数据恢复验证
5. 服务恢复确认
```

## 📊 数据字典

### 核心实体关系图
```mermaid
erDiagram
    SysUser ||--o{ BizCase : "负责"
    BizClient ||--o{ BizCase : "拥有"
    BizClient ||--o{ BizContract : "签署"
    BizCase ||--o{ BizDocument : "关联"
    BizContract ||--o{ BizDocument : "关联"
    BizCase ||--o{ BizFinanceRecord : "产生"
    BizContract ||--o{ BizFinanceRecord : "产生"
    SysUser ||--o{ SysRole : "拥有"
    SysRole ||--o{ SysPermission : "包含"

    SysUser {
        bigint id PK
        string username UK
        string password
        string real_name
        string email
        string phone
        int status
        bigint role_id FK
    }

    BizClient {
        bigint id PK
        string client_name
        int client_type
        string contact_person
        string phone
        string email
        int status
    }

    BizCase {
        bigint id PK
        string case_number UK
        string case_title
        string case_type
        string case_status
        bigint client_id FK
        bigint lawyer_id FK
        decimal case_amount
    }

    BizContract {
        bigint id PK
        string contract_number UK
        string contract_title
        string contract_type
        string contract_status
        bigint client_id FK
        decimal contract_amount
    }
```

### 数据表详细说明

#### 用户表 (sys_user)
| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | BIGINT | - | PK | 主键ID |
| username | VARCHAR | 50 | UK,NOT NULL | 用户名 |
| password | VARCHAR | 100 | NOT NULL | 密码(加密) |
| real_name | VARCHAR | 50 | - | 真实姓名 |
| email | VARCHAR | 100 | - | 邮箱 |
| phone | VARCHAR | 20 | - | 手机号 |
| avatar | VARCHAR | 255 | - | 头像URL |
| status | TINYINT | - | DEFAULT 1 | 状态:0-禁用,1-启用 |
| role_id | BIGINT | - | FK | 角色ID |
| dept_id | BIGINT | - | FK | 部门ID |
| last_login_time | DATETIME | - | - | 最后登录时间 |
| last_login_ip | VARCHAR | 50 | - | 最后登录IP |

#### 客户表 (biz_client)
| 字段名 | 类型 | 长度 | 约束 | 说明 |
|--------|------|------|------|------|
| id | BIGINT | - | PK | 主键ID |
| client_name | VARCHAR | 100 | NOT NULL | 客户名称 |
| client_type | TINYINT | - | NOT NULL | 客户类型:1-个人,2-企业,3-组织 |
| contact_person | VARCHAR | 50 | - | 联系人 |
| phone | VARCHAR | 20 | - | 联系电话 |
| email | VARCHAR | 100 | - | 邮箱 |
| address | VARCHAR | 255 | - | 地址 |
| industry | VARCHAR | 50 | - | 所属行业 |
| credit_code | VARCHAR | 50 | - | 统一社会信用代码 |
| legal_person | VARCHAR | 50 | - | 法定代表人 |
| registered_capital | DECIMAL | 15,2 | - | 注册资本 |
| status | TINYINT | - | DEFAULT 1 | 状态:0-停用,1-正常 |

## 🔧 系统配置

### 环境配置参数

#### 开发环境配置
```yaml
# 数据库配置
database:
  host: localhost
  port: 3306
  name: cloud_legal_dev
  username: dev_user
  password: dev_password
  pool_size: 10

# Redis配置
redis:
  host: localhost
  port: 6379
  database: 0
  timeout: 5000

# 文件存储配置
file_storage:
  type: local
  path: /data/uploads
  max_size: 10MB
  allowed_types: [jpg, jpeg, png, pdf, doc, docx]
```

#### 生产环境配置
```yaml
# 数据库配置
database:
  host: prod-db.example.com
  port: 3306
  name: cloud_legal_prod
  username: prod_user
  password: ${DB_PASSWORD}
  pool_size: 50
  ssl: true

# Redis配置
redis:
  host: prod-redis.example.com
  port: 6379
  database: 0
  password: ${REDIS_PASSWORD}
  timeout: 3000

# 文件存储配置
file_storage:
  type: minio
  endpoint: https://minio.example.com
  access_key: ${MINIO_ACCESS_KEY}
  secret_key: ${MINIO_SECRET_KEY}
  bucket: cloud-legal-files
```

### 系统参数配置

#### 业务参数
```json
{
  "business_config": {
    "case_auto_archive_days": 30,
    "contract_expire_remind_days": [30, 15, 7, 1],
    "client_inactive_days": 90,
    "max_file_upload_size": "10MB",
    "supported_file_types": ["pdf", "doc", "docx", "jpg", "jpeg", "png"],
    "default_page_size": 10,
    "max_page_size": 100
  }
}
```

#### 安全参数
```json
{
  "security_config": {
    "jwt_expire_hours": 24,
    "refresh_token_expire_days": 7,
    "password_min_length": 6,
    "password_max_length": 50,
    "login_max_attempts": 5,
    "login_lock_minutes": 30,
    "session_timeout_minutes": 120
  }
}
```

## 📋 API接口规范

### 接口设计原则

#### RESTful API规范
```
HTTP方法使用规范:
- GET: 查询数据，幂等操作
- POST: 创建数据，非幂等操作
- PUT: 更新数据，幂等操作
- DELETE: 删除数据，幂等操作
- PATCH: 部分更新，幂等操作

URL设计规范:
- 使用名词复数形式: /api/clients
- 层级关系: /api/clients/{id}/cases
- 查询参数: /api/clients?page=1&size=10
- 版本控制: /api/v1/clients
```

#### 请求响应格式
```json
// 请求格式
{
  "data": {
    "client_name": "测试客户",
    "client_type": 2,
    "contact_person": "张三",
    "phone": "13800138000"
  }
}

// 成功响应格式
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "client_name": "测试客户",
    "client_type": 2,
    "created_time": "2024-01-01 10:00:00"
  },
  "timestamp": 1704067200000
}

// 错误响应格式
{
  "code": 400,
  "message": "参数错误",
  "errors": [
    {
      "field": "client_name",
      "message": "客户名称不能为空"
    }
  ],
  "timestamp": 1704067200000
}
```

### 核心API接口

#### 认证接口
```
POST /api/auth/login
功能: 用户登录
请求参数:
{
  "username": "admin",
  "password": "123456",
  "remember_me": false
}
响应数据:
{
  "access_token": "eyJhbGciOiJIUzI1NiIs...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
  "token_type": "Bearer",
  "expires_in": 86400,
  "user_info": {...}
}
```

#### 客户管理接口
```
GET /api/clients
功能: 分页查询客户列表
请求参数:
- page: 页码 (默认1)
- size: 每页大小 (默认10)
- keyword: 搜索关键词
- client_type: 客户类型
- status: 客户状态

POST /api/clients
功能: 创建客户
请求参数:
{
  "client_name": "客户名称",
  "client_type": 2,
  "contact_person": "联系人",
  "phone": "联系电话"
}

PUT /api/clients/{id}
功能: 更新客户信息

DELETE /api/clients/{id}
功能: 删除客户
```

## 🧪 测试用例

### 功能测试用例

#### 用户登录测试
```
测试用例ID: TC_LOGIN_001
测试标题: 正常用户名密码登录
前置条件: 用户已注册且状态正常
测试步骤:
1. 打开登录页面
2. 输入正确的用户名: admin
3. 输入正确的密码: 123456
4. 点击登录按钮
预期结果: 登录成功，跳转到工作台页面

测试用例ID: TC_LOGIN_002
测试标题: 错误密码登录
前置条件: 用户已注册
测试步骤:
1. 打开登录页面
2. 输入正确的用户名: admin
3. 输入错误的密码: wrong_password
4. 点击登录按钮
预期结果: 登录失败，显示"用户名或密码错误"
```

#### 客户管理测试
```
测试用例ID: TC_CLIENT_001
测试标题: 创建企业客户
前置条件: 用户已登录且有客户管理权限
测试步骤:
1. 进入客户管理页面
2. 点击"新增客户"按钮
3. 选择客户类型为"企业"
4. 填写必填字段
5. 点击保存按钮
预期结果: 客户创建成功，列表中显示新客户

测试用例ID: TC_CLIENT_002
测试标题: 客户信息验证
前置条件: 用户已登录
测试步骤:
1. 进入客户管理页面
2. 点击"新增客户"按钮
3. 不填写必填字段
4. 点击保存按钮
预期结果: 显示字段验证错误信息
```

### 性能测试用例

#### 并发登录测试
```
测试场景: 并发用户登录
测试目标: 验证系统在高并发下的登录性能
测试参数:
- 并发用户数: 100
- 测试时长: 5分钟
- 预期响应时间: < 2秒
- 预期成功率: > 99%

测试步骤:
1. 准备100个测试账号
2. 使用JMeter模拟并发登录
3. 监控系统资源使用情况
4. 记录响应时间和成功率
```

#### 数据查询性能测试
```
测试场景: 大数据量查询
测试目标: 验证分页查询性能
测试数据: 10万条客户记录
测试参数:
- 查询类型: 分页查询、条件查询、模糊查询
- 预期响应时间: < 1秒
- 数据库连接池: 20个连接

测试步骤:
1. 准备测试数据
2. 执行各种查询操作
3. 监控数据库性能
4. 记录查询响应时间
```

## 🚀 部署运维

### 部署架构

#### 单机部署架构
```
服务器配置:
- CPU: 4核心
- 内存: 8GB
- 硬盘: 100GB SSD
- 操作系统: CentOS 7.x / Ubuntu 18.04+

软件环境:
- JDK 17
- MySQL 8.0
- Redis 7.0
- Nginx 1.20+
- Node.js 18+

部署结构:
┌─────────────────────────────────┐
│         Nginx (80/443)          │
├─────────────────────────────────┤
│    Frontend (Vue.js App)       │
├─────────────────────────────────┤
│   Backend (Spring Boot:8080)   │
├─────────────────────────────────┤
│     MySQL (3306) + Redis       │
└─────────────────────────────────┘
```

#### 集群部署架构
```
负载均衡层:
- Nginx集群 (主备模式)
- Keepalived高可用

应用层:
- 前端: 2台Web服务器
- 后端: 3台应用服务器

数据层:
- MySQL主从复制 (1主2从)
- Redis哨兵模式 (3节点)
- 文件存储: MinIO集群

监控层:
- Prometheus + Grafana
- ELK日志分析
- Zabbix系统监控
```

### 运维监控

#### 监控指标
```yaml
# 系统监控指标
system_metrics:
  cpu_usage: "< 80%"
  memory_usage: "< 85%"
  disk_usage: "< 90%"
  network_io: "< 80%"
  load_average: "< 4.0"

# 应用监控指标
application_metrics:
  response_time: "< 2s"
  error_rate: "< 1%"
  throughput: "> 100 req/s"
  active_sessions: "< 1000"
  jvm_heap_usage: "< 80%"

# 数据库监控指标
database_metrics:
  connection_usage: "< 80%"
  slow_query_count: "< 10/min"
  replication_lag: "< 1s"
  table_lock_waits: "< 5/min"
```

#### 告警规则
```yaml
# 告警配置
alerts:
  critical:
    - name: "服务不可用"
      condition: "http_status != 200"
      duration: "1m"
      action: "立即通知"

    - name: "数据库连接失败"
      condition: "mysql_up == 0"
      duration: "30s"
      action: "立即通知"

  warning:
    - name: "CPU使用率过高"
      condition: "cpu_usage > 80%"
      duration: "5m"
      action: "邮件通知"

    - name: "内存使用率过高"
      condition: "memory_usage > 85%"
      duration: "5m"
      action: "邮件通知"
```

---

**文档版本**: v1.0.0
**最后更新**: 2024-01-01
**文档状态**: 正式版
**下次更新**: 2024-04-01
