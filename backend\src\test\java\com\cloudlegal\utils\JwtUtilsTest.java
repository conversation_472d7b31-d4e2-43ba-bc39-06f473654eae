package com.cloudlegal.utils;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JWT工具类测试
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@SpringBootTest
@TestPropertySource(properties = {
    "jwt.secret=cloudLegalSecretKey2024ForJWTTokenGenerationWithHS512AlgorithmSupportAndSecureKeyLength2024!@#$%^&*()",
    "jwt.expiration=86400000",
    "jwt.refresh-expiration=604800000",
    "jwt.issuer=cloud-legal-system"
})
class JwtUtilsTest {

    @Autowired
    private JwtUtils jwtUtils;

    @Test
    void testKeyStrength() {
        // 测试密钥强度
        assertTrue(jwtUtils.validateKeyStrength(), "JWT密钥强度应该满足HS512要求");

        Map<String, Object> keyInfo = jwtUtils.getKeyInfo();
        System.out.println("密钥信息: " + keyInfo);

        assertTrue((Integer) keyInfo.get("keyLengthBits") >= 512, "密钥长度应该至少512位");
        assertTrue((Boolean) keyInfo.get("isSecure"), "密钥应该是安全的");
    }

    @Test
    void testTokenGeneration() {
        // 测试Token生成
        Map<String, Object> claims = Map.of(
            "userId", 1L,
            "username", "testuser",
            "roleId", 1L
        );

        // 测试分钟级Token生成
        String token1 = jwtUtils.generateToken(claims, 30);
        assertNotNull(token1, "Token不应该为空");
        assertTrue(token1.startsWith("eyJ"), "Token应该以JWT头部开始");

        // 测试TimeUnit Token生成
        String token2 = jwtUtils.generateToken(claims, 1);
        assertNotNull(token2, "Token不应该为空");
        assertTrue(token2.startsWith("eyJ"), "Token应该以JWT头部开始");

        // 测试访问Token生成
        String accessToken = jwtUtils.generateAccessToken(claims);
        assertNotNull(accessToken, "访问Token不应该为空");

        // 测试刷新Token生成
        String refreshToken = jwtUtils.generateRefreshToken(claims);
        assertNotNull(refreshToken, "刷新Token不应该为空");
    }

    @Test
    void testTokenValidation() {
        // 测试Token验证
        Map<String, Object> claims = Map.of(
            "userId", 1L,
            "username", "testuser",
            "roleId", 1L
        );

        String token = jwtUtils.generateToken(claims, 30);

        // 验证Token有效性
        assertTrue(jwtUtils.validateToken(token), "Token应该是有效的");

        // 验证Token未过期
        assertFalse(jwtUtils.isTokenExpired(token), "Token不应该过期");

        // 获取用户ID
        Long userId = jwtUtils.getUserIdFromToken(token);
        assertEquals(1L, userId, "用户ID应该匹配");

        // 获取用户名
        String username = jwtUtils.getUsernameFromToken(token);
        assertEquals("testuser", username, "用户名应该匹配");
    }

    @Test
    void testTokenRemainingTime() {
        // 测试Token剩余时间
        Map<String, Object> claims = Map.of(
            "userId", 1L,
            "username", "testuser",
            "roleId", 1L
        );

        String token = jwtUtils.generateToken(claims, 1);

        // 获取剩余时间（秒）
        long remainingSeconds = jwtUtils.getTokenRemainingTime(token);
        assertTrue(remainingSeconds > 3500 && remainingSeconds <= 3600,
                   "剩余时间应该接近1小时: " + remainingSeconds);

        // 获取剩余时间（分钟）
        long remainingMinutes = jwtUtils.getTokenRemainingTime(token);
        assertTrue(remainingMinutes >= 58 && remainingMinutes <= 60,
                   "剩余时间应该接近60分钟: " + remainingMinutes);

        // 获取剩余时间（小时）
        long remainingHours = jwtUtils.getTokenRemainingTime(token);
        assertEquals(0, remainingHours, "剩余时间应该是0小时（不足1小时）");
    }

    @Test
    void testTokenRefresh() {
        // 测试Token刷新
        Map<String, Object> claims = Map.of(
            "userId", 1L,
            "username", "testuser",
            "roleId", 1L
        );

        String originalToken = jwtUtils.generateToken(claims, 30);
        String refreshedToken = jwtUtils.refreshToken(originalToken, 60);

        assertNotNull(refreshedToken, "刷新后的Token不应该为空");
        assertNotEquals(originalToken, refreshedToken, "刷新后的Token应该与原Token不同");

        // 验证刷新后的Token有效
        assertTrue(jwtUtils.validateToken(refreshedToken), "刷新后的Token应该有效");

        // 验证载荷信息保持一致
        assertEquals(jwtUtils.getUserIdFromToken(originalToken),
                    jwtUtils.getUserIdFromToken(refreshedToken),
                    "用户ID应该保持一致");
    }

    @Test
    void testCreateTestToken() {
        // 测试创建测试Token
        String testToken1 = jwtUtils.createTestToken(1L, "testuser", 1L, 30);
        assertNotNull(testToken1, "测试Token不应该为空");

        String testToken2 = jwtUtils.createTestToken(1L, "testuser", 1L, 2);
        assertNotNull(testToken2, "测试Token不应该为空");

        // 验证Token内容
        assertEquals(1L, jwtUtils.getUserIdFromToken(testToken1), "用户ID应该匹配");
        assertEquals("testuser", jwtUtils.getUsernameFromToken(testToken1), "用户名应该匹配");
    }

    @Test
    void testInvalidToken() {
        // 测试无效Token
        assertFalse(jwtUtils.validateToken("invalid.token.here"), "无效Token应该验证失败");
        assertFalse(jwtUtils.validateToken(""), "空Token应该验证失败");
        assertFalse(jwtUtils.validateToken(null), "null Token应该验证失败");

        assertNull(jwtUtils.getUserIdFromToken("invalid.token.here"), "无效Token应该返回null用户ID");
        assertNull(jwtUtils.getUsernameFromToken("invalid.token.here"), "无效Token应该返回null用户名");

        assertEquals(-1, jwtUtils.getTokenRemainingTime("invalid.token.here"),
                    "无效Token剩余时间应该是-1");
    }
}
