# 前端代理配置说明

## 配置概述

本项目使用 Vite 开发服务器的代理功能来解决开发环境下的跨域问题。

## 配置文件

### 1. Vite 配置 (`vite.config.ts`)

```typescript
server: {
  port: 3000,
  host: '0.0.0.0',
  open: true,
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      secure: false
    }
  }
}
```

### 2. 环境变量配置

#### 开发环境 (`.env.development`)
```env
VITE_API_BASE_URL=/api
VITE_BACKEND_URL=http://localhost:8080
```

#### 生产环境 (`.env.production`)
```env
VITE_API_BASE_URL=http://your-backend-domain.com/api
```

### 3. Axios 配置 (`src/utils/request.ts`)

```typescript
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: import.meta.env.VITE_API_TIMEOUT || 10000
})
```

## 请求流程

1. **前端发起请求**: `axios.get('/api/user/info')`
2. **Vite 代理拦截**: 匹配到 `/api` 规则
3. **转发到后端**: `http://localhost:8080/api/user/info`
4. **后端处理**: Spring Boot 应用处理请求
5. **返回响应**: 通过代理返回给前端

## 后端配置要求

确保后端 `application.yml` 配置正确：

```yaml
server:
  port: 8080
  servlet:
    context-path: /api

app:
  cors:
    allowed-origins: 
      - http://localhost:3000
      - http://localhost:5173
```

## 常见问题

### 1. 代理不生效
- 检查 Vite 开发服务器是否正常启动
- 确认代理配置路径匹配
- 查看浏览器网络面板的请求地址

### 2. 跨域错误
- 确保后端 CORS 配置包含前端地址
- 检查 `changeOrigin: true` 配置

### 3. 连接被拒绝
- 确保后端服务在 localhost:8080 运行
- 检查防火墙设置

## 测试代理配置

运行测试脚本：
```bash
node scripts/test-proxy.js
```

## 部署注意事项

生产环境不使用代理，需要：
1. 配置正确的后端域名
2. 确保后端 CORS 配置
3. 可能需要 Nginx 反向代理
