# Maven 打包配置说明

## 概述

本项目已配置Maven打包时过滤测试文件，支持多种打包方式。

## 环境要求

### 必需软件
- **Java 17+**: 项目使用Java 17编译
- **Maven 3.6+**: 用于项目构建和依赖管理

### 环境变量配置
确保以下环境变量已正确配置：
```bash
# Java
JAVA_HOME=C:\Program Files\Java\jdk-17
PATH=%JAVA_HOME%\bin;%PATH%

# Maven
MAVEN_HOME=C:\Program Files\Apache\maven
PATH=%MAVEN_HOME%\bin;%PATH%
```

### 验证环境
```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version
```

## 配置说明

### 1. Maven插件配置

在 `pom.xml` 中配置了以下插件来过滤测试文件：

- **maven-surefire-plugin**: 跳过单元测试执行
- **maven-resources-plugin**: 过滤测试资源文件
- **maven-compiler-plugin**: 排除测试源码编译
- **maven-jar-plugin**: 排除测试类打包

### 2. Profile配置

- **prod**: 生产环境，跳过所有测试
- **dev**: 开发环境（默认），可以运行测试

## 使用方法

### 方法一：使用脚本（推荐）

#### Windows环境
```bash
# 运行打包脚本
build.bat
```

#### Linux/Mac环境
```bash
# 给脚本执行权限（首次运行）
chmod +x build.sh

# 运行打包脚本
./build.sh
```

### 方法二：直接使用Maven命令

#### 生产环境打包（跳过测试）
```bash
# 使用profile
mvn clean package -Pprod

# 或者直接跳过测试
mvn clean package -DskipTests=true -Dmaven.test.skip=true

# 快速打包（跳过测试和文档）
mvn clean package -DskipTests=true -Dmaven.test.skip=true -Dmaven.javadoc.skip=true
```

#### 开发环境打包（包含测试）
```bash
mvn clean package -Pdev
```

#### 只编译不打包
```bash
mvn clean compile -DskipTests=true
```

#### 清理项目
```bash
mvn clean
```

### 方法三：IDE配置

在IDEA中可以通过以下方式配置：

1. 打开 `Run/Debug Configurations`
2. 创建新的Maven配置
3. 在 `Command line` 中输入：`clean package -Pprod`
4. 在 `Profiles` 中选择 `prod`

## 过滤规则

### 测试文件过滤规则
- `**/*Test.java` - 所有以Test结尾的Java文件
- `**/*Tests.java` - 所有以Tests结尾的Java文件
- `**/test/**` - test目录下的所有文件
- `**/*.test.properties` - 测试配置文件
- `**/*.test.yml` - 测试YAML配置文件
- `**/*.test.yaml` - 测试YAML配置文件

### 测试类过滤规则
- `**/*Test.class` - 编译后的测试类
- `**/*Tests.class` - 编译后的测试类
- `**/test/**` - test目录下的所有编译文件

## 验证打包结果

打包完成后，可以通过以下方式验证：

```bash
# 查看JAR包内容
jar -tf target/cloud-legal-backend-1.0.0.jar | grep -i test

# 如果没有输出，说明测试文件已被过滤
```

## 注意事项

1. **生产环境**：建议使用 `-Pprod` profile，确保不包含测试代码
2. **开发环境**：可以使用 `-Pdev` profile，保留测试功能
3. **CI/CD**：在持续集成中建议使用生产环境配置
4. **文件大小**：过滤测试文件后，JAR包大小会显著减小

## 常见问题

### Q: 为什么要过滤测试文件？
A: 
- 减小JAR包大小
- 提高安全性，避免测试代码泄露
- 加快打包速度
- 符合生产环境部署规范

### Q: 如何在特定情况下包含测试文件？
A: 使用开发环境profile：`mvn clean package -Pdev`

### Q: 打包失败怎么办？
A: 
1. 检查Java版本是否为17
2. 确保Maven版本兼容
3. 运行 `mvn clean` 清理后重试
4. 检查网络连接，确保能下载依赖

## 相关文件

- `pom.xml` - Maven配置文件
- `build.bat` - Windows打包脚本
- `build.sh` - Linux/Mac打包脚本
- `.mvn/maven.config` - Maven全局配置
