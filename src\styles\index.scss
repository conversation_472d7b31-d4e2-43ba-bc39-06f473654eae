@use './variables.scss' as *;

// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  color: $text-color-primary;
  background-color: $bg-color;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 通用类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mr-10 {
  margin-right: 10px;
}

.mr-20 {
  margin-right: 20px;
}

// 卡片样式
.card {
  background: #fff;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-base;
  padding: 20px;
  margin-bottom: 20px;
}

// 表格工具栏
.table-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}

// 状态标签
.status-tag {
  &.pending {
    color: $warning-color;
  }
  
  &.processing {
    color: $primary-color;
  }
  
  &.completed {
    color: $success-color;
  }
  
  &.closed {
    color: $info-color;
  }
}

// 优先级标签
.priority-tag {
  &.low {
    color: $info-color;
  }

  &.medium {
    color: $warning-color;
  }

  &.high {
    color: $danger-color;
  }

  &.urgent {
    color: $danger-color;
    font-weight: bold;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .el-aside {
    width: 64px !important;
  }

  .table-toolbar {
    flex-direction: column;
    gap: 10px;

    .toolbar-left,
    .toolbar-right {
      width: 100%;
      justify-content: flex-start;
    }
  }
}

@media (max-width: 768px) {
  .el-main {
    padding: 10px !important;
  }

  .card {
    padding: 15px !important;
    margin-bottom: 15px !important;
  }

  .stats-section {
    .el-col {
      margin-bottom: 15px;
    }
  }

  .table-toolbar {
    .toolbar-left,
    .toolbar-right {
      flex-wrap: wrap;
      gap: 8px;
    }
  }

  .el-table {
    font-size: 12px;

    .el-table__cell {
      padding: 8px 0;
    }
  }

  .pagination {
    .el-pagination {
      justify-content: center;
    }
  }
}

@media (max-width: 480px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;

    .header-actions {
      width: 100%;
      justify-content: flex-start;
    }
  }

  .el-descriptions {
    .el-descriptions__body {
      .el-descriptions__table {
        .el-descriptions__cell {
          padding: 8px !important;
        }
      }
    }
  }
}

// 动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;

  .loading-text {
    margin-left: 10px;
    color: $text-color-secondary;
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: $text-color-secondary;

  .empty-icon {
    font-size: 64px;
    color: $border-color-base;
    margin-bottom: 20px;
  }

  .empty-text {
    font-size: 16px;
    margin-bottom: 10px;
  }

  .empty-description {
    font-size: 14px;
    color: $text-color-placeholder;
  }
}

// 工具提示
.tooltip-content {
  max-width: 300px;
  word-wrap: break-word;
}

// 表单优化
.form-section {
  margin-bottom: 30px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid $border-color-light;
  }
}

// 按钮组优化
.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;

  @media (max-width: 480px) {
    .el-button {
      flex: 1;
      min-width: 0;
    }
  }
}
