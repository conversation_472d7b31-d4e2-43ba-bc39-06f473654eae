-------------------------------------------------------------------------------
Test set: com.cloudlegal.utils.JwtUtilsTest
-------------------------------------------------------------------------------
Tests run: 7, Failures: 0, Errors: 3, Skipped: 0, Time elapsed: 17.20 s <<< FAILURE! -- in com.cloudlegal.utils.JwtUtilsTest
com.cloudlegal.utils.JwtUtilsTest.testTokenRemainingTime -- Time elapsed: 2.288 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method generateToken(Map<String,Object>, int) in the type JwtUtils is not applicable for the arguments (Map<String,Object>, int, TimeUnit)
	The method getTokenRemainingTime(String) in the type JwtUtils is not applicable for the arguments (String, TimeUnit)
	The method getTokenRemainingTime(String) in the type JwtUtils is not applicable for the arguments (String, TimeUnit)

	at com.cloudlegal.utils.JwtUtilsTest.testTokenRemainingTime(JwtUtilsTest.java:106)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.cloudlegal.utils.JwtUtilsTest.testTokenGeneration -- Time elapsed: 0.004 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problem: 
	The method generateToken(Map<String,Object>, int) in the type JwtUtils is not applicable for the arguments (Map<String,Object>, int, TimeUnit)

	at com.cloudlegal.utils.JwtUtilsTest.testTokenGeneration(JwtUtilsTest.java:58)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.cloudlegal.utils.JwtUtilsTest.testCreateTestToken -- Time elapsed: 0.004 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problem: 
	The method createTestToken(Long, String, Long, int) in the type JwtUtils is not applicable for the arguments (long, String, long, int, TimeUnit)

	at com.cloudlegal.utils.JwtUtilsTest.testCreateTestToken(JwtUtilsTest.java:153)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

