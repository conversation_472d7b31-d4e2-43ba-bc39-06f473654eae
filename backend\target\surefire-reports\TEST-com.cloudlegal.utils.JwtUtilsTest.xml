<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.cloudlegal.utils.JwtUtilsTest" time="17.196" tests="7" errors="3" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\qingfeng\code\java\aicode\cloud-legal\backend\target\test-classes;D:\qingfeng\code\java\aicode\cloud-legal\backend\target\classes;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-web\3.2.0\spring-boot-starter-web-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter\3.2.0\spring-boot-starter-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-logging\3.2.0\spring-boot-starter-logging-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;D:\local\apache-maven-3.8.8\mvn_lib\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;D:\local\apache-maven-3.8.8\mvn_lib\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-json\3.2.0\spring-boot-starter-json-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.3\jackson-datatype-jdk8-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.3\jackson-datatype-jsr310-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.3\jackson-module-parameter-names-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-tomcat\3.2.0\spring-boot-starter-tomcat-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\tomcat\embed\tomcat-embed-core\10.1.16\tomcat-embed-core-10.1.16.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.16\tomcat-embed-websocket-10.1.16.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-web\6.1.1\spring-web-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-beans\6.1.1\spring-beans-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\micrometer\micrometer-observation\1.12.0\micrometer-observation-1.12.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\micrometer\micrometer-commons\1.12.0\micrometer-commons-1.12.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-webmvc\6.1.1\spring-webmvc-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-context\6.1.1\spring-context-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-expression\6.1.1\spring-expression-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-security\3.2.0\spring-boot-starter-security-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-aop\6.1.1\spring-aop-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\security\spring-security-config\6.2.0\spring-security-config-6.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\security\spring-security-web\6.2.0\spring-security-web-6.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-data-redis\3.2.0\spring-boot-starter-data-redis-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\lettuce\lettuce-core\6.3.0.RELEASE\lettuce-core-6.3.0.RELEASE.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\projectreactor\reactor-core\3.6.0\reactor-core-3.6.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\data\spring-data-redis\3.2.0\spring-data-redis-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\data\spring-data-keyvalue\3.2.0\spring-data-keyvalue-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\data\spring-data-commons\3.2.0\spring-data-commons-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-tx\6.1.1\spring-tx-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-oxm\6.1.1\spring-oxm-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-validation\3.2.0\spring-boot-starter-validation-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\tomcat\embed\tomcat-embed-el\10.1.16\tomcat-embed-el-10.1.16.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-mail\3.2.0\spring-boot-starter-mail-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-context-support\6.1.1\spring-context-support-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\eclipse\angus\jakarta.mail\2.0.2\jakarta.mail-2.0.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\eclipse\angus\angus-activation\2.0.1\angus-activation-2.0.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-spring-boot3-starter\3.5.12\mybatis-plus-spring-boot3-starter-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus\3.5.12\mybatis-plus-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-core\3.5.12\mybatis-plus-core-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-annotation\3.5.12\mybatis-plus-annotation-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-spring\3.5.12\mybatis-plus-spring-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-extension\3.5.12\mybatis-plus-extension-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\mybatis\mybatis\3.5.19\mybatis-3.5.19.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\mybatis\mybatis-spring\3.0.4\mybatis-spring-3.0.4.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.12\mybatis-plus-spring-boot-autoconfigure-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-autoconfigure\3.2.0\spring-boot-autoconfigure-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-jdbc\3.2.0\spring-boot-starter-jdbc-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-jdbc\6.1.1\spring-jdbc-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-generator\3.5.12\mybatis-plus-generator-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\jsonwebtoken\jjwt-api\0.11.5\jjwt-api-0.11.5.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\jsonwebtoken\jjwt-impl\0.11.5\jjwt-impl-0.11.5.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\jsonwebtoken\jjwt-jackson\0.11.5\jjwt-jackson-0.11.5.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\alibaba\fastjson\2.0.43\fastjson-2.0.43.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\alibaba\fastjson2\fastjson2-extension\2.0.43\fastjson2-extension-2.0.43.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\alibaba\fastjson2\fastjson2\2.0.43\fastjson2-2.0.43.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\github\xiaoymin\knife4j-openapi3-jakarta-spring-boot-starter\4.3.0\knife4j-openapi3-jakarta-spring-boot-starter-4.3.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\github\xiaoymin\knife4j-core\4.3.0\knife4j-core-4.3.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\github\xiaoymin\knife4j-openapi3-ui\4.3.0\knife4j-openapi3-ui-4.3.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springdoc\springdoc-openapi-starter-webmvc-ui\2.0.4\springdoc-openapi-starter-webmvc-ui-2.0.4.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springdoc\springdoc-openapi-starter-webmvc-api\2.0.4\springdoc-openapi-starter-webmvc-api-2.0.4.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springdoc\springdoc-openapi-starter-common\2.0.4\springdoc-openapi-starter-common-2.0.4.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\swagger\core\v3\swagger-core-jakarta\2.2.8\swagger-core-jakarta-2.2.8.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\swagger\core\v3\swagger-annotations-jakarta\2.2.8\swagger-annotations-jakarta-2.2.8.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\swagger\core\v3\swagger-models-jakarta\2.2.8\swagger-models-jakarta-2.2.8.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.3\jackson-dataformat-yaml-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\webjars\swagger-ui\4.18.1\swagger-ui-4.18.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\webjars\webjars-locator-core\0.55\webjars-locator-core-0.55.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\github\classgraph\classgraph\4.8.149\classgraph-4.8.149.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\minio\minio\8.5.7\minio-8.5.7.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\carrotsearch\thirdparty\simple-xml-safe\2.7.1\simple-xml-safe-2.7.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\google\guava\guava\32.1.3-jre\guava-32.1.3-jre.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\checkerframework\checker-qual\3.37.0\checker-qual-3.37.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\google\errorprone\error_prone_annotations\2.21.1\error_prone_annotations-2.21.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\squareup\okhttp3\okhttp\4.12.0\okhttp-4.12.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.20\kotlin-stdlib-common-1.9.20.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.20\kotlin-stdlib-jdk8-1.9.20.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\jetbrains\kotlin\kotlin-stdlib\1.9.20\kotlin-stdlib-1.9.20.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\jetbrains\annotations\13.0\annotations-13.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.20\kotlin-stdlib-jdk7-1.9.20.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\bouncycastle\bcprov-jdk18on\1.76\bcprov-jdk18on-1.76.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\commons\commons-compress\1.24.0\commons-compress-1.24.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\xerial\snappy\snappy-java\1.1.10.5\snappy-java-1.1.10.5.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\commons\commons-lang3\3.13.0\commons-lang3-3.13.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\cn\hutool\hutool-all\5.8.22\hutool-all-5.8.22.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-test\3.2.0\spring-boot-starter-test-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-test\3.2.0\spring-boot-test-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-test-autoconfigure\3.2.0\spring-boot-test-autoconfigure-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\objenesis\objenesis\3.3\objenesis-3.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-core\6.1.1\spring-core-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-jcl\6.1.1\spring-jcl-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-test\6.1.1\spring-test-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\security\spring-security-test\6.2.0\spring-security-test-6.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\security\spring-security-core\6.2.0\spring-security-core-6.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\security\spring-security-crypto\6.2.0\spring-security-crypto-6.2.0.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="D:\local\jdk-21.0.4\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire10806844638018164338\surefirebooter-20250728104053311_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire10806844638018164338 2025-07-28T10-40-43_744-jvmRun1 surefire-20250728104053311_1tmp surefire_0-20250728104053311_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="D:\qingfeng\code\java\aicode\cloud-legal\backend\target\test-classes;D:\qingfeng\code\java\aicode\cloud-legal\backend\target\classes;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-web\3.2.0\spring-boot-starter-web-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter\3.2.0\spring-boot-starter-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-logging\3.2.0\spring-boot-starter-logging-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;D:\local\apache-maven-3.8.8\mvn_lib\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;D:\local\apache-maven-3.8.8\mvn_lib\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-json\3.2.0\spring-boot-starter-json-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.3\jackson-datatype-jdk8-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.3\jackson-datatype-jsr310-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.3\jackson-module-parameter-names-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-tomcat\3.2.0\spring-boot-starter-tomcat-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\tomcat\embed\tomcat-embed-core\10.1.16\tomcat-embed-core-10.1.16.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.16\tomcat-embed-websocket-10.1.16.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-web\6.1.1\spring-web-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-beans\6.1.1\spring-beans-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\micrometer\micrometer-observation\1.12.0\micrometer-observation-1.12.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\micrometer\micrometer-commons\1.12.0\micrometer-commons-1.12.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-webmvc\6.1.1\spring-webmvc-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-context\6.1.1\spring-context-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-expression\6.1.1\spring-expression-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-security\3.2.0\spring-boot-starter-security-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-aop\6.1.1\spring-aop-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\security\spring-security-config\6.2.0\spring-security-config-6.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\security\spring-security-web\6.2.0\spring-security-web-6.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-data-redis\3.2.0\spring-boot-starter-data-redis-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\lettuce\lettuce-core\6.3.0.RELEASE\lettuce-core-6.3.0.RELEASE.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\projectreactor\reactor-core\3.6.0\reactor-core-3.6.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\data\spring-data-redis\3.2.0\spring-data-redis-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\data\spring-data-keyvalue\3.2.0\spring-data-keyvalue-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\data\spring-data-commons\3.2.0\spring-data-commons-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-tx\6.1.1\spring-tx-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-oxm\6.1.1\spring-oxm-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-validation\3.2.0\spring-boot-starter-validation-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\tomcat\embed\tomcat-embed-el\10.1.16\tomcat-embed-el-10.1.16.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-mail\3.2.0\spring-boot-starter-mail-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-context-support\6.1.1\spring-context-support-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\eclipse\angus\jakarta.mail\2.0.2\jakarta.mail-2.0.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\eclipse\angus\angus-activation\2.0.1\angus-activation-2.0.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-spring-boot3-starter\3.5.12\mybatis-plus-spring-boot3-starter-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus\3.5.12\mybatis-plus-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-core\3.5.12\mybatis-plus-core-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-annotation\3.5.12\mybatis-plus-annotation-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-spring\3.5.12\mybatis-plus-spring-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-extension\3.5.12\mybatis-plus-extension-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\mybatis\mybatis\3.5.19\mybatis-3.5.19.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\mybatis\mybatis-spring\3.0.4\mybatis-spring-3.0.4.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.12\mybatis-plus-spring-boot-autoconfigure-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-autoconfigure\3.2.0\spring-boot-autoconfigure-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-jdbc\3.2.0\spring-boot-starter-jdbc-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-jdbc\6.1.1\spring-jdbc-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\baomidou\mybatis-plus-generator\3.5.12\mybatis-plus-generator-3.5.12.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\jsonwebtoken\jjwt-api\0.11.5\jjwt-api-0.11.5.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\jsonwebtoken\jjwt-impl\0.11.5\jjwt-impl-0.11.5.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\jsonwebtoken\jjwt-jackson\0.11.5\jjwt-jackson-0.11.5.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\alibaba\fastjson\2.0.43\fastjson-2.0.43.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\alibaba\fastjson2\fastjson2-extension\2.0.43\fastjson2-extension-2.0.43.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\alibaba\fastjson2\fastjson2\2.0.43\fastjson2-2.0.43.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\github\xiaoymin\knife4j-openapi3-jakarta-spring-boot-starter\4.3.0\knife4j-openapi3-jakarta-spring-boot-starter-4.3.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\github\xiaoymin\knife4j-core\4.3.0\knife4j-core-4.3.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\github\xiaoymin\knife4j-openapi3-ui\4.3.0\knife4j-openapi3-ui-4.3.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springdoc\springdoc-openapi-starter-webmvc-ui\2.0.4\springdoc-openapi-starter-webmvc-ui-2.0.4.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springdoc\springdoc-openapi-starter-webmvc-api\2.0.4\springdoc-openapi-starter-webmvc-api-2.0.4.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springdoc\springdoc-openapi-starter-common\2.0.4\springdoc-openapi-starter-common-2.0.4.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\swagger\core\v3\swagger-core-jakarta\2.2.8\swagger-core-jakarta-2.2.8.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\swagger\core\v3\swagger-annotations-jakarta\2.2.8\swagger-annotations-jakarta-2.2.8.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\swagger\core\v3\swagger-models-jakarta\2.2.8\swagger-models-jakarta-2.2.8.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.3\jackson-dataformat-yaml-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\webjars\swagger-ui\4.18.1\swagger-ui-4.18.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\webjars\webjars-locator-core\0.55\webjars-locator-core-0.55.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\github\classgraph\classgraph\4.8.149\classgraph-4.8.149.jar;D:\local\apache-maven-3.8.8\mvn_lib\io\minio\minio\8.5.7\minio-8.5.7.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\carrotsearch\thirdparty\simple-xml-safe\2.7.1\simple-xml-safe-2.7.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\google\guava\guava\32.1.3-jre\guava-32.1.3-jre.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\checkerframework\checker-qual\3.37.0\checker-qual-3.37.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\google\errorprone\error_prone_annotations\2.21.1\error_prone_annotations-2.21.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\squareup\okhttp3\okhttp\4.12.0\okhttp-4.12.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\squareup\okio\okio\3.6.0\okio-3.6.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\squareup\okio\okio-jvm\3.6.0\okio-jvm-3.6.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.20\kotlin-stdlib-common-1.9.20.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.20\kotlin-stdlib-jdk8-1.9.20.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\jetbrains\kotlin\kotlin-stdlib\1.9.20\kotlin-stdlib-1.9.20.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\jetbrains\annotations\13.0\annotations-13.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.20\kotlin-stdlib-jdk7-1.9.20.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\bouncycastle\bcprov-jdk18on\1.76\bcprov-jdk18on-1.76.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\commons\commons-compress\1.24.0\commons-compress-1.24.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\xerial\snappy\snappy-java\1.1.10.5\snappy-java-1.1.10.5.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apache\commons\commons-lang3\3.13.0\commons-lang3-3.13.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\cn\hutool\hutool-all\5.8.22\hutool-all-5.8.22.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-starter-test\3.2.0\spring-boot-starter-test-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-test\3.2.0\spring-boot-test-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\boot\spring-boot-test-autoconfigure\3.2.0\spring-boot-test-autoconfigure-3.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\objenesis\objenesis\3.3\objenesis-3.3.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-core\6.1.1\spring-core-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-jcl\6.1.1\spring-jcl-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\spring-test\6.1.1\spring-test-6.1.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\security\spring-security-test\6.2.0\spring-security-test-6.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\security\spring-security-core\6.2.0\spring-security-core-6.2.0.jar;D:\local\apache-maven-3.8.8\mvn_lib\org\springframework\security\spring-security-crypto\6.2.0\spring-security-crypto-6.2.0.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\local\jdk-21.0.4"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="D:\qingfeng\code\java\aicode\cloud-legal\backend"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire10806844638018164338\surefirebooter-20250728104053311_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.4+8-LTS-274"/>
    <property name="user.name" value="wxl_3"/>
    <property name="stdout.encoding" value="GBK"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="LOG_FILE" value="logs/cloud-legal.log"/>
    <property name="localRepository" value="D:\local\apache-maven-3.8.8\mvn_lib"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="idea.version" value="2023.1.2"/>
    <property name="java.version" value="21.0.4"/>
    <property name="user.dir" value="D:\qingfeng\code\java\aicode\cloud-legal\backend"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="19072"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\local\jdk-21.0.4\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;D:\ProgramData\anaconda3\condabin;D:\local\jdk-11.0.7\bin;D:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\System32\HWAudioDriverLibs;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\Program Files (x86)\Tencent\微信web开发者工具\dll;D:\Pro;ram Files (x86)\NetSarang\Xshell 7\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\windows\system32\HWAudioDriver\;D:\Users\wxl_3\AppData\Roaming\nvm;D:\Program Files\nodejs;D:\Program Files\Git\bin;D:\local\phpstudy_pro\Extensions\composer2.5.8;D:\local\phpstudy_pro\Extensions\php\php7.4.3nts;D:\ProgramData\anaconda3;D:\ProgramData\anaconda3\Scripts;D:\ProgramData\anaconda3\Library\bin;D:\Program Files\Git\cmd;d:\Users\wxl_3\AppData\Local\Programs\Trae CN\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\JetBrains\IntelliJ IDEA 2023.1.2\bin;D:\Users\wxl_3\AppData\Local\Programs\Microsoft VS Code\bin;D:\Users\wxl_3\AppData\Local\Programs\Fiddler;;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="GBK"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="21.0.4+8-LTS-274"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
    <property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"/>
    <property name="LOGGED_APPLICATION_NAME" value="[cloud-legal-backend] "/>
  </properties>
  <testcase name="testTokenRemainingTime" classname="com.cloudlegal.utils.JwtUtilsTest" time="2.288">
    <error message="Unresolved compilation problems: &#10;	The method generateToken(Map&lt;String,Object&gt;, int) in the type JwtUtils is not applicable for the arguments (Map&lt;String,Object&gt;, int, TimeUnit)&#10;	The method getTokenRemainingTime(String) in the type JwtUtils is not applicable for the arguments (String, TimeUnit)&#10;	The method getTokenRemainingTime(String) in the type JwtUtils is not applicable for the arguments (String, TimeUnit)&#10;" type="java.lang.Error"><![CDATA[java.lang.Error: 
Unresolved compilation problems: 
	The method generateToken(Map<String,Object>, int) in the type JwtUtils is not applicable for the arguments (Map<String,Object>, int, TimeUnit)
	The method getTokenRemainingTime(String) in the type JwtUtils is not applicable for the arguments (String, TimeUnit)
	The method getTokenRemainingTime(String) in the type JwtUtils is not applicable for the arguments (String, TimeUnit)

	at com.cloudlegal.utils.JwtUtilsTest.testTokenRemainingTime(JwtUtilsTest.java:106)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
]]></error>
    <system-out><![CDATA[10:40:57.879 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils -- Could not detect default configuration classes for test class [com.cloudlegal.utils.JwtUtilsTest]: JwtUtilsTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
10:40:58.165 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper -- Found @SpringBootConfiguration com.cloudlegal.CloudLegalApplication for test class com.cloudlegal.utils.JwtUtilsTest

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v3.2.0)

2025-07-28 10:40:59 [main] INFO  com.cloudlegal.utils.JwtUtilsTest - Starting JwtUtilsTest using Java 21.0.4 with PID 19072 (started by wxl_3 in D:\qingfeng\code\java\aicode\cloud-legal\backend)
2025-07-28 10:40:59 [main] DEBUG com.cloudlegal.utils.JwtUtilsTest - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-07-28 10:40:59 [main] INFO  com.cloudlegal.utils.JwtUtilsTest - The following 1 profile is active: "dev"
2025-07-28 10:41:03 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 10:41:03 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 10:41:03 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 54 ms. Found 0 Redis repository interfaces.
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Get /************* network interface 
Get network interface info: name:wireless_32768 (Intel(R) Wi-Fi 6 AX201 160MHz)
Initialization Sequence datacenterId:29 workerId:28
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        3.5.12 
2025-07-28 10:41:09 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: f9a76da3-dcf0-485f-a844-78eed86def02

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-28 10:41:11 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@69fd99c1, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@32d8710a, org.springframework.security.web.context.SecurityContextHolderFilter@62c46e53, org.springframework.security.web.header.HeaderWriterFilter@1a7a21d0, org.springframework.web.filter.CorsFilter@180cc0df, org.springframework.security.web.authentication.logout.LogoutFilter@7c5d36c3, com.cloudlegal.filter.JwtAuthenticationFilter@64f33dee, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@35d81657, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@42ef5216, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@61c58320, org.springframework.security.web.session.SessionManagementFilter@28a3fc34, org.springframework.security.web.access.ExceptionTranslationFilter@25c8c71e, org.springframework.security.web.access.intercept.AuthorizationFilter@70e5737f]
2025-07-28 10:41:11 [main] INFO  c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.web.context.support.GenericWebApplicationContext@5bb8f9e2
2025-07-28 10:41:11 [main] INFO  com.cloudlegal.utils.JwtUtilsTest - Started JwtUtilsTest in 13.4 seconds (process running for 17.574)
]]></system-out>
    <system-err><![CDATA[WARNING: A Java agent has been loaded dynamically (D:\local\apache-maven-3.8.8\mvn_lib\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
]]></system-err>
  </testcase>
  <testcase name="testTokenValidation" classname="com.cloudlegal.utils.JwtUtilsTest" time="0.112"/>
  <testcase name="testTokenGeneration" classname="com.cloudlegal.utils.JwtUtilsTest" time="0.004">
    <error message="Unresolved compilation problem: &#10;	The method generateToken(Map&lt;String,Object&gt;, int) in the type JwtUtils is not applicable for the arguments (Map&lt;String,Object&gt;, int, TimeUnit)&#10;" type="java.lang.Error"><![CDATA[java.lang.Error: 
Unresolved compilation problem: 
	The method generateToken(Map<String,Object>, int) in the type JwtUtils is not applicable for the arguments (Map<String,Object>, int, TimeUnit)

	at com.cloudlegal.utils.JwtUtilsTest.testTokenGeneration(JwtUtilsTest.java:58)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
]]></error>
  </testcase>
  <testcase name="testKeyStrength" classname="com.cloudlegal.utils.JwtUtilsTest" time="0.004">
    <system-out><![CDATA[密钥信息: {algorithm=HS512, keyFormat=String, isSecure=true, minRequiredBits=512, keyLength=101, isBase64Encoded=false, keyLengthBits=808}
]]></system-out>
  </testcase>
  <testcase name="testTokenRefresh" classname="com.cloudlegal.utils.JwtUtilsTest" time="0.015"/>
  <testcase name="testCreateTestToken" classname="com.cloudlegal.utils.JwtUtilsTest" time="0.004">
    <error message="Unresolved compilation problem: &#10;	The method createTestToken(Long, String, Long, int) in the type JwtUtils is not applicable for the arguments (long, String, long, int, TimeUnit)&#10;" type="java.lang.Error"><![CDATA[java.lang.Error: 
Unresolved compilation problem: 
	The method createTestToken(Long, String, Long, int) in the type JwtUtils is not applicable for the arguments (long, String, long, int, TimeUnit)

	at com.cloudlegal.utils.JwtUtilsTest.testCreateTestToken(JwtUtilsTest.java:153)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
]]></error>
  </testcase>
  <testcase name="testInvalidToken" classname="com.cloudlegal.utils.JwtUtilsTest" time="0.014"/>
</testsuite>