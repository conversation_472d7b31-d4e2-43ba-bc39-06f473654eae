# 🔧 故障排除指南

## 导航栏菜单无法切换问题

### 问题描述
点击侧边栏菜单项时可能出现以下问题：
- 菜单无法切换到对应页面
- 控制台报错
- 页面白屏或加载失败

### 可能的原因

1. **路由配置问题**
   - 子路由路径配置错误（应该是相对路径，不是绝对路径）
   - 组件导入路径错误
   - 路由元信息配置错误

2. **组件加载问题**
   - 组件文件不存在
   - 组件内部有语法错误
   - 依赖导入失败

3. **图标组件问题**
   - 使用了不存在的图标名称
   - 图标组件未正确导入

### 解决方案

#### 1. 检查路由配置

确保 `src/router/index.ts` 中的子路由使用相对路径：

```typescript
// ✅ 正确的配置
children: [
  {
    path: 'dashboard',  // 相对路径
    name: 'Dashboard',
    component: () => import('@/views/dashboard/index.vue'),
    meta: { title: '工作台', icon: 'House' }
  }
]

// ❌ 错误的配置
children: [
  {
    path: '/dashboard',  // 绝对路径会导致问题
    name: 'Dashboard',
    component: () => import('@/views/dashboard/index.vue'),
    meta: { title: '工作台', icon: 'House' }
  }
]
```

#### 2. 检查组件文件

确保所有页面组件文件都存在：
- `src/views/dashboard/index.vue`
- `src/views/cases/index.vue`
- `src/views/contracts/index.vue`
- `src/views/clients/index.vue`
- `src/views/documents/index.vue`
- `src/views/finance/index.vue`
- `src/views/users/index.vue`
- `src/views/settings/index.vue`

#### 3. 检查图标名称

确保使用的图标名称在Element Plus中存在：
- `House` ✅
- `Folder` ✅
- `Document` ✅
- `User` ✅
- `Money` ✅
- `UserFilled` ✅
- `Setting` ✅

#### 4. 调试步骤

1. **打开浏览器开发者工具**
   - 按F12或右键选择"检查"
   - 查看Console标签页的错误信息

2. **检查网络请求**
   - 查看Network标签页
   - 确认组件文件是否成功加载

3. **使用测试页面**
   - 访问 `/test` 路由
   - 测试所有路由链接是否正常工作

#### 5. 临时解决方案

如果问题持续存在，可以使用以下临时解决方案：

1. **手动导航**
   ```typescript
   // 在菜单点击事件中手动导航
   const handleMenuClick = (path: string) => {
     router.push(path)
   }
   ```

2. **重新启动开发服务器**
   ```bash
   # 停止服务器 (Ctrl+C)
   # 清除缓存
   rm -rf node_modules/.vite
   # 重新启动
   npm run dev
   ```

3. **检查依赖**
   ```bash
   # 重新安装依赖
   rm -rf node_modules
   npm install
   ```

### 常见错误信息

#### "Failed to resolve component"
- **原因**: 组件文件不存在或路径错误
- **解决**: 检查组件文件路径和文件是否存在

#### "Cannot resolve module"
- **原因**: 导入路径错误或依赖缺失
- **解决**: 检查导入语句和安装相关依赖

#### "Icon component not found"
- **原因**: 使用了不存在的图标名称
- **解决**: 使用正确的Element Plus图标名称

### 预防措施

1. **使用TypeScript严格模式**
2. **定期检查控制台错误**
3. **使用ESLint进行代码检查**
4. **保持依赖版本更新**

### 获取帮助

如果问题仍然存在，请：
1. 复制完整的错误信息
2. 说明复现步骤
3. 提供浏览器和Node.js版本信息
