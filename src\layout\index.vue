<template>
  <div class="layout">
    <!-- 移动端遮罩层 -->
    <div
      v-if="isMobile && isMobileMenuOpen"
      class="mobile-overlay"
      @click="isMobileMenuOpen = false"
    ></div>

    <el-container>
      <!-- 侧边栏 -->
      <el-aside
        :width="isCollapse ? '64px' : '200px'"
        class="sidebar"
        :class="{ 'mobile-open': isMobileMenuOpen }"
      >
        <div class="logo">
          <Logo :show-text="!isCollapse" />
        </div>
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          router
          class="sidebar-menu"
          @select="handleMenuSelect"
        >
          <template v-for="route in menuRoutes" :key="route.path">
            <el-menu-item
              v-if="!route.meta?.hidden"
              :index="route.path"
              :disabled="route.meta?.disabled"
            >
              <el-icon v-if="route.meta?.icon">
                <component :is="route.meta.icon" />
              </el-icon>
              <template #title>{{ route.meta?.title }}</template>
            </el-menu-item>
          </template>
        </el-menu>
      </el-aside>

      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button
              type="text"
              @click="toggleCollapse"
              class="collapse-btn"
            >
              <el-icon>
                <Expand v-if="isCollapse" />
                <Fold v-else />
              </el-icon>
            </el-button>
            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                v-for="item in breadcrumbs"
                :key="item.path"
                :to="item.path"
              >
                {{ item.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="header-right">
            <el-dropdown @command="handleCommand">
              <span class="user-info">
                <el-avatar :size="32" :src="userStore.userInfo?.avatar">
                  {{ userStore.userInfo?.realName?.charAt(0) }}
                </el-avatar>
                <span class="username">{{ userStore.userInfo?.realName }}</span>
                <el-icon><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                  <el-dropdown-item command="settings">设置</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主内容区 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessageBox } from 'element-plus'
import Logo from '@/components/Logo.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const isCollapse = ref(false)
const isMobileMenuOpen = ref(false)
const isMobile = ref(false)

// 检测是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
  if (!isMobile.value) {
    isMobileMenuOpen.value = false
  }
}

// 当前激活的菜单
const activeMenu = computed(() => {
  const path = route.path
  // 对于详情页面，激活对应的列表页面
  if (path.includes('/detail/')) {
    return path.split('/detail/')[0]
  }
  return path
})

// 菜单路由
const menuRoutes = computed(() => {
  const mainRoute = router.getRoutes().find(r => r.path === '/')
  if (!mainRoute?.children) return []

  return mainRoute.children
    .filter(child => child.meta?.title) // 只显示有标题的路由
    .map(child => ({
      ...child,
      path: child.path.startsWith('/') ? child.path : '/' + child.path
    }))
})

// 面包屑
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta?.title
  }))
})

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  if (isMobile.value) {
    isMobileMenuOpen.value = !isMobileMenuOpen.value
  } else {
    isCollapse.value = !isCollapse.value
  }
}

// 处理菜单选择
const handleMenuSelect = (index: string) => {
  console.log('菜单选择:', index)
  if (isMobile.value) {
    isMobileMenuOpen.value = false
  }
  // Element Plus的router属性会自动处理路由跳转
}

// 处理用户下拉菜单命令
const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人中心
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await userStore.logout()
        router.push('/login')
      } catch (error) {
        // 用户取消
      }
      break
  }
}

// 监听窗口大小变化
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)

  // 调试信息
  console.log('🔍 布局组件已挂载')
  console.log('当前路由:', route.path)
  console.log('菜单路由:', menuRoutes.value)
  console.log('激活菜单:', activeMenu.value)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style lang="scss" scoped>
.layout {
  height: 100vh;

  .sidebar {
    background: #304156;
    transition: width 0.3s;
    box-shadow: 2px 0 6px rgba(0, 0, 0, 0.1);

    .logo {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      border-bottom: 1px solid #263445;
      transition: all 0.3s ease;

      :deep(.logo-text) {
        color: #fff;
        font-size: 18px;
        font-weight: bold;
      }
    }

    .sidebar-menu {
      border: none;
      background: #304156;

      :deep(.el-menu-item) {
        color: #bfcbd9;
        width: 100% !important;
        height: 56px !important;
        line-height: 56px !important;
        padding: 0 20px !important;
        margin: 0 !important;
        border-radius: 0 !important;

        &:hover {
          background: #263445 !important;
          color: #fff !important;
        }

        &.is-active {
          background: #409eff !important;
          color: #fff !important;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: #fff;
          }
        }

        // 图标样式
        .el-icon {
          margin-right: 8px;
          font-size: 18px;
          width: 18px;
          text-align: center;
        }

        // 标题样式
        .el-menu-item__title {
          font-size: 14px;
          font-weight: 500;
        }
      }

      // 折叠状态下的样式
      &.el-menu--collapse {
        :deep(.el-menu-item) {
          padding: 0 !important;
          text-align: center;

          .el-icon {
            margin-right: 0;
            font-size: 20px;
          }
        }
      }
    }
  }

  .header {
    background: #fff;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;

    .header-left {
      display: flex;
      align-items: center;
      flex: 1;

      .collapse-btn {
        margin-right: 20px;
        font-size: 18px;
      }

      .el-breadcrumb {
        flex: 1;
        overflow: hidden;
      }
    }

    .header-right {
      .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;

        .username {
          margin: 0 8px;

          @media (max-width: 768px) {
            display: none;
          }
        }
      }
    }
  }

  .main-content {
    background: #f0f2f5;
    padding: 20px;

    @media (max-width: 768px) {
      padding: 10px;
    }
  }
}

// 移动端遮罩层
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

// 移动端适配
@media (max-width: 768px) {
  .layout {
    .sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      z-index: 1000;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
      width: 200px !important; // 移动端固定宽度

      &.mobile-open {
        transform: translateX(0);
      }

      .sidebar-menu {
        :deep(.el-menu-item) {
          width: 100% !important;
          padding: 0 20px !important;
        }
      }
    }

    .header {
      padding: 0 15px;

      .header-left {
        .collapse-btn {
          margin-right: 15px;
        }
      }
    }

    .main-content {
      margin-left: 0 !important;
    }
  }
}

// 平板适配
@media (max-width: 1024px) and (min-width: 769px) {
  .layout {
    .header {
      .header-left {
        .el-breadcrumb {
          :deep(.el-breadcrumb__item) {
            .el-breadcrumb__inner {
              max-width: 150px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
}
</style>
