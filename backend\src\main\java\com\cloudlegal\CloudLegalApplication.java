package com.cloudlegal;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 云法务系统启动类
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@SpringBootApplication
@MapperScan("com.cloudlegal.mapper")
@EnableTransactionManagement
@EnableCaching
@EnableAsync
public class CloudLegalApplication {

    public static void main(String[] args) {
        SpringApplication.run(CloudLegalApplication.class, args);
        System.out.println("=================================");
        System.out.println("云法务系统启动成功！");
        System.out.println("接口文档地址：http://localhost:8080/api/doc.html");
        System.out.println("=================================");
    }
}
