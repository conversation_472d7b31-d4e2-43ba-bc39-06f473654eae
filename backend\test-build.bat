@echo off
echo ========================================
echo 测试Maven打包配置
echo ========================================

echo.
echo 1. 测试生产环境打包（跳过测试）
echo ----------------------------------------
echo 命令: mvn clean package -Pprod
echo.

echo 2. 检查是否有Maven
where mvn >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Maven命令，请确保Maven已安装并配置到PATH
    echo.
    echo 安装Maven步骤：
    echo 1. 下载Maven: https://maven.apache.org/download.cgi
    echo 2. 解压到目录，如: C:\Program Files\Apache\maven
    echo 3. 设置环境变量:
    echo    MAVEN_HOME=C:\Program Files\Apache\maven
    echo    PATH=%%MAVEN_HOME%%\bin;%%PATH%%
    echo.
    pause
    exit /b 1
)

echo [成功] 找到Maven命令
mvn -version
echo.

echo 3. 检查Java版本
java -version 2>&1 | findstr "version" | findstr "17\|18\|19\|20\|21"
if %errorlevel% neq 0 (
    echo [警告] 建议使用Java 17或更高版本
)
echo.

echo 4. 显示当前有效的POM配置（生产环境）
echo ----------------------------------------
mvn help:effective-pom -Pprod -q | findstr -i "skipTests\|maven.test.skip"
echo.

echo 5. 测试编译（不打包）
echo ----------------------------------------
mvn clean compile -Pprod
if %errorlevel% neq 0 (
    echo [错误] 编译失败
    pause
    exit /b 1
)

echo.
echo [成功] 配置测试完成！
echo.
echo 现在可以使用以下命令进行打包：
echo   生产环境: mvn clean package -Pprod
echo   开发环境: mvn clean package -Pdev
echo   使用脚本: build.bat
echo.
pause
