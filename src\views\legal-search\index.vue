<template>
  <div class="legal-search-page">
    <div class="page-header">
      <h1>法律检索</h1>
      <p>司法案例、法律法规快速检索</p>
    </div>

    <div class="development-notice">
      <div class="notice-card">
        <el-icon class="notice-icon"><Tools /></el-icon>
        <div class="notice-content">
          <h2>功能开发中</h2>
          <p>法律检索功能正在紧张开发中，即将为您提供：</p>
          <ul>
            <li>📚 <strong>司法案例检索</strong> - 海量裁判文书智能检索</li>
            <li>📖 <strong>法律法规检索</strong> - 最新法律法规快速查找</li>
            <li>📄 <strong>合同模板检索</strong> - 专业合同模板库</li>
            <li>🔍 <strong>智能搜索</strong> - AI驱动的精准搜索</li>
            <li>📊 <strong>案例分析</strong> - 相似案例对比分析</li>
          </ul>
          <div class="progress-info">
            <p><strong>开发进度：</strong></p>
            <el-progress :percentage="75" status="success" />
            <p class="progress-text">预计 2024年8月 上线</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 预览功能界面 -->
    <div class="preview-section">
      <h2>功能预览</h2>
      <div class="preview-tabs">
        <el-tabs v-model="activeTab" class="demo-tabs">
          <el-tab-pane label="司法案例" name="cases">
            <div class="search-demo">
              <div class="search-bar">
                <el-input
                  placeholder="请输入案例关键词，如：合同纠纷、知识产权..."
                  disabled
                >
                  <template #append>
                    <el-button disabled>搜索</el-button>
                  </template>
                </el-input>
              </div>
              <div class="demo-results">
                <div class="result-item">
                  <h4>某科技公司与某投资公司合同纠纷案</h4>
                  <p class="case-info">
                    <span>案件性质：民事纠纷</span>
                    <span>审理法院：北京市朝阳区人民法院</span>
                    <span>审理时间：2023年</span>
                  </p>
                  <p class="case-summary">
                    本案涉及软件开发合同履行争议，法院认为合同条款明确，当事人应按约履行...
                  </p>
                  <div class="case-tags">
                    <el-tag size="small">合同纠纷</el-tag>
                    <el-tag size="small" type="success">胜诉</el-tag>
                  </div>
                </div>
                <div class="result-item">
                  <h4>知识产权侵权纠纷典型案例</h4>
                  <p class="case-info">
                    <span>案件性质：知识产权纠纷</span>
                    <span>审理法院：上海知识产权法院</span>
                    <span>审理时间：2023年</span>
                  </p>
                  <p class="case-summary">
                    涉及商标权侵权认定标准，法院从商标近似性、商品类似性等方面进行分析...
                  </p>
                  <div class="case-tags">
                    <el-tag size="small">知识产权</el-tag>
                    <el-tag size="small" type="warning">部分胜诉</el-tag>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="法律法规" name="laws">
            <div class="search-demo">
              <div class="search-bar">
                <el-input
                  placeholder="请输入法规名称或关键词..."
                  disabled
                >
                  <template #append>
                    <el-button disabled>搜索</el-button>
                  </template>
                </el-input>
              </div>
              <div class="demo-results">
                <div class="result-item">
                  <h4>中华人民共和国合同法</h4>
                  <p class="law-info">
                    <span>发布机关：全国人大常委会</span>
                    <span>施行日期：1999年10月1日</span>
                    <span>状态：已废止</span>
                  </p>
                  <p class="law-summary">
                    为了保护合同当事人的合法权益，维护社会经济秩序，促进社会主义现代化建设...
                  </p>
                </div>
                <div class="result-item">
                  <h4>中华人民共和国民法典</h4>
                  <p class="law-info">
                    <span>发布机关：全国人大</span>
                    <span>施行日期：2021年1月1日</span>
                    <span>状态：现行有效</span>
                  </p>
                  <p class="law-summary">
                    为了保护民事主体的合法权益，调整民事关系，维护社会和经济秩序...
                  </p>
                </div>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="合同模板" name="templates">
            <div class="search-demo">
              <div class="search-bar">
                <el-input
                  placeholder="请输入合同类型，如：劳动合同、买卖合同..."
                  disabled
                >
                  <template #append>
                    <el-button disabled>搜索</el-button>
                  </template>
                </el-input>
              </div>
              <div class="demo-results">
                <div class="template-grid">
                  <div class="template-item">
                    <el-icon class="template-icon"><Document /></el-icon>
                    <h4>劳动合同模板</h4>
                    <p>标准劳动合同范本，包含基本条款</p>
                    <div class="template-meta">
                      <span>下载：1,234次</span>
                      <span>评分：4.8分</span>
                    </div>
                  </div>
                  <div class="template-item">
                    <el-icon class="template-icon"><Tickets /></el-icon>
                    <h4>买卖合同模板</h4>
                    <p>商品买卖合同标准模板</p>
                    <div class="template-meta">
                      <span>下载：856次</span>
                      <span>评分：4.6分</span>
                    </div>
                  </div>
                  <div class="template-item">
                    <el-icon class="template-icon"><Reading /></el-icon>
                    <h4>服务合同模板</h4>
                    <p>专业服务合同范本</p>
                    <div class="template-meta">
                      <span>下载：692次</span>
                      <span>评分：4.7分</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 联系我们 -->
    <div class="contact-section">
      <div class="contact-card">
        <h3>期待您的反馈</h3>
        <p>如果您对法律检索功能有任何建议或需求，欢迎联系我们：</p>
        <div class="contact-info">
          <div class="contact-item">
            <el-icon><Message /></el-icon>
            <span>邮箱：<EMAIL></span>
          </div>
          <div class="contact-item">
            <el-icon><Phone /></el-icon>
            <span>电话：************</span>
          </div>
        </div>
        <el-button type="primary" @click="handleFeedback">
          提交反馈
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const activeTab = ref('cases')

const handleFeedback = () => {
  ElMessage.info('感谢您的关注！我们会认真考虑您的建议。')
}
</script>

<style lang="scss" scoped>
.legal-search-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    h1 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 28px;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 16px;
    }
  }

  .development-notice {
    margin-bottom: 40px;
    
    .notice-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;
      padding: 40px;
      border-radius: 12px;
      display: flex;
      align-items: flex-start;
      gap: 20px;
      
      .notice-icon {
        font-size: 48px;
        opacity: 0.8;
        flex-shrink: 0;
      }
      
      .notice-content {
        flex: 1;
        
        h2 {
          margin: 0 0 16px 0;
          font-size: 24px;
        }
        
        p {
          margin: 0 0 16px 0;
          font-size: 16px;
          opacity: 0.9;
        }
        
        ul {
          margin: 0 0 24px 0;
          padding-left: 20px;
          
          li {
            margin: 8px 0;
            font-size: 15px;
            
            strong {
              font-weight: 600;
            }
          }
        }
        
        .progress-info {
          background: rgba(255, 255, 255, 0.1);
          padding: 20px;
          border-radius: 8px;
          
          p {
            margin: 0 0 12px 0;
            font-weight: 600;
          }
          
          .progress-text {
            margin: 12px 0 0 0;
            font-size: 14px;
            opacity: 0.8;
          }
        }
      }
    }
  }

  .preview-section {
    margin-bottom: 40px;
    
    h2 {
      margin: 0 0 24px 0;
      color: #303133;
      font-size: 20px;
      text-align: center;
    }
    
    .preview-tabs {
      background: #fff;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      padding: 20px;
      
      .search-demo {
        .search-bar {
          margin-bottom: 24px;
        }
        
        .demo-results {
          .result-item {
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            margin-bottom: 16px;
            opacity: 0.7;
            
            h4 {
              margin: 0 0 12px 0;
              color: #303133;
              font-size: 16px;
            }
            
            .case-info, .law-info {
              margin: 0 0 12px 0;
              font-size: 14px;
              color: #909399;
              
              span {
                margin-right: 16px;
              }
            }
            
            .case-summary, .law-summary {
              margin: 0 0 12px 0;
              color: #606266;
              line-height: 1.5;
            }
            
            .case-tags {
              display: flex;
              gap: 8px;
            }
          }
          
          .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            
            .template-item {
              padding: 20px;
              border: 1px solid #e4e7ed;
              border-radius: 6px;
              text-align: center;
              opacity: 0.7;
              
              .template-icon {
                font-size: 32px;
                color: #409eff;
                margin-bottom: 12px;
              }
              
              h4 {
                margin: 0 0 8px 0;
                color: #303133;
              }
              
              p {
                margin: 0 0 12px 0;
                color: #606266;
                font-size: 14px;
              }
              
              .template-meta {
                font-size: 12px;
                color: #909399;
                
                span {
                  margin-right: 12px;
                }
              }
            }
          }
        }
      }
    }
  }

  .contact-section {
    .contact-card {
      background: #f8f9fa;
      padding: 30px;
      border-radius: 8px;
      text-align: center;
      
      h3 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 18px;
      }
      
      p {
        margin: 0 0 20px 0;
        color: #606266;
      }
      
      .contact-info {
        display: flex;
        justify-content: center;
        gap: 40px;
        margin-bottom: 24px;
        
        .contact-item {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #606266;
          
          .el-icon {
            color: #409eff;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .legal-search-page {
    padding: 15px;
    
    .development-notice .notice-card {
      flex-direction: column;
      text-align: center;
      padding: 30px 20px;
    }
    
    .contact-section .contact-card .contact-info {
      flex-direction: column;
      gap: 16px;
    }
  }
}
</style>
