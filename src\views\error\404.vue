<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-image">
        <img src="/404.svg" alt="404" />
      </div>
      <div class="error-info">
        <h1>404</h1>
        <h2>页面不存在</h2>
        <p>抱歉，您访问的页面不存在或已被删除</p>
        <div class="error-actions">
          <el-button type="primary" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.error-page {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  .error-content {
    text-align: center;
    color: #fff;
    
    .error-image {
      margin-bottom: 40px;
      
      img {
        width: 300px;
        height: 200px;
        opacity: 0.8;
      }
    }
    
    .error-info {
      h1 {
        font-size: 120px;
        font-weight: bold;
        margin: 0;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }
      
      h2 {
        font-size: 32px;
        margin: 20px 0;
        font-weight: 500;
      }
      
      p {
        font-size: 18px;
        margin: 20px 0 40px 0;
        opacity: 0.9;
      }
      
      .error-actions {
        display: flex;
        gap: 16px;
        justify-content: center;
        
        .el-button {
          padding: 12px 24px;
          font-size: 16px;
        }
      }
    }
  }
}
</style>
