<template>
  <el-dialog
    v-model="dialogVisible"
    title="合同模板管理"
    width="800px"
    @close="handleClose"
  >
    <div class="template-toolbar">
      <el-button type="primary" @click="handleAddTemplate">
        <el-icon><Plus /></el-icon>
        新增模板
      </el-button>
    </div>

    <el-table :data="templateList" stripe>
      <el-table-column prop="name" label="模板名称" />
      <el-table-column prop="type" label="模板类型" width="120" />
      <el-table-column prop="description" label="描述" show-overflow-tooltip />
      <el-table-column prop="createdAt" label="创建时间" width="160">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleUseTemplate(row)">使用</el-button>
          <el-button type="success" size="small" @click="handleEditTemplate(row)">编辑</el-button>
          <el-button type="danger" size="small" @click="handleDeleteTemplate(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getContractTemplates } from '@/api/contract'
import { formatDate } from '@/utils'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const templateList = ref<any[]>([])

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 获取模板列表
const fetchTemplateList = async () => {
  try {
    const data = await getContractTemplates()
    templateList.value = data
  } catch (error) {
    console.error('获取模板列表失败:', error)
    // 模拟数据
    templateList.value = [
      {
        id: 1,
        name: '服务合同模板',
        type: 'service',
        description: '标准服务合同模板',
        createdAt: new Date()
      },
      {
        id: 2,
        name: '采购合同模板',
        type: 'purchase',
        description: '标准采购合同模板',
        createdAt: new Date()
      }
    ]
  }
}

// 新增模板
const handleAddTemplate = () => {
  ElMessage.info('新增模板功能开发中')
}

// 使用模板
const handleUseTemplate = (template: any) => {
  ElMessage.success(`已选择模板：${template.name}`)
  emit('success')
  handleClose()
}

// 编辑模板
const handleEditTemplate = (template: any) => {
  ElMessage.info('编辑模板功能开发中')
}

// 删除模板
const handleDeleteTemplate = async (template: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除模板 "${template.name}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    ElMessage.success('删除成功')
    fetchTemplateList()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

onMounted(() => {
  fetchTemplateList()
})
</script>

<style lang="scss" scoped>
.template-toolbar {
  margin-bottom: 20px;
}
</style>
