server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: cloud-legal-backend
  
  profiles:
    active: dev
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************
    username: fd
    password: 123456
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # Redis配置（暂时禁用）
  data:
    redis:
      host: *************
      port: 6380
      password:
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-email-password
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/**/*Mapper.xml

# JWT配置
jwt:
  # 512位安全密钥，使用Base64编码，满足HS512算法要求（64字节=512位）
  secret: lOpF9f+/rtNhGmDjRSOW7C5gSQIuamH4X5l28feH1h47r5H/xBDeNGuGXpk4RZUgjCU1RpgIuGy3XOOF4pC7rQ==
  expiration: 86400000 # 24小时 (24 * 60 * 60 * 1000)
  refresh-expiration: 604800000 # 7天 (7 * 24 * 60 * 60 * 1000)
  issuer: cloud-legal-system

# 文件存储配置
minio:
  endpoint: http://*************:9001
  access-key: minioadmin
  secret-key: minioadmin
  bucket-name: cloud-legal

# 日志配置
logging:
  level:
    com.cloudlegal: debug
    org.springframework.security: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/cloud-legal.log

# Knife4j配置
knife4j:
  enable: true
  openapi:
    title: 云法务系统API文档
    description: 云法务系统后端接口文档
    version: 1.0.0
    concat: <EMAIL>
  setting:
    language: zh_cn

# 应用自定义配置
app:
  # 跨域配置 - 允许所有源访问（开发环境）
  cors:
    allowed-origins:
      - "*"
    allowed-methods: 
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed-headers: "*"
    allow-credentials: true
  
  # 安全配置
  security:
    # 不需要认证的路径
    permit-all-paths:
      - /auth/**
      - /public/**
      - /doc.html
      - /webjars/**
      - /swagger-resources/**
      - /v3/api-docs/**
      - /favicon.ico
      - /error
    
    # 需要管理员权限的路径
    admin-paths:
      - /admin/**
      - /system/**
  
  # 业务配置
  business:
    # 默认密码
    default-password: 123456
    # 文件上传路径
    upload-path: /uploads/
    # 允许的文件类型
    allowed-file-types:
      - jpg
      - jpeg
      - png
      - gif
      - pdf
      - doc
      - docx
      - xls
      - xlsx
      - ppt
      - pptx
      - txt
    # 最大文件大小(MB)
    max-file-size: 10
