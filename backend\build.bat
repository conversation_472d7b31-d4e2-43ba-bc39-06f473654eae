@echo off
echo ========================================
echo Maven 打包脚本
echo ========================================

set /p choice="请选择打包方式: [1]生产环境(跳过测试) [2]开发环境(包含测试) [3]清理项目: "

if "%choice%"=="1" (
    echo 开始生产环境打包...
    echo 跳过测试文件
    mvn clean package -Pprod -DskipTests=true -Dmaven.test.skip=true
    echo 生产环境打包完成！
) else if "%choice%"=="2" (
    echo 开始开发环境打包...
    echo 包含测试文件
    mvn clean package -Pdev
    echo 开发环境打包完成！
) else if "%choice%"=="3" (
    echo 清理项目...
    mvn clean
    echo 清理完成！
) else (
    echo 无效选择，默认使用生产环境打包
    mvn clean package -Pprod -DskipTests=true -Dmaven.test.skip=true
    echo 生产环境打包完成！
)

pause
