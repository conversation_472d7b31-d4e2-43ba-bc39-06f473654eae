<template>
  <div class="legal-calculator-page">
    <div class="page-header">
      <h1>法律计算器</h1>
      <p>律师费、诉讼费、利息计算</p>
    </div>

    <div class="calculator-container">
      <div class="calculator-tabs">
        <div 
          class="tab-item" 
          :class="{ active: activeCalculator === 'lawyer-fee' }"
          @click="activeCalculator = 'lawyer-fee'"
        >
          <el-icon><Money /></el-icon>
          律师费计算
        </div>
        <div 
          class="tab-item" 
          :class="{ active: activeCalculator === 'court-fee' }"
          @click="activeCalculator = 'court-fee'"
        >
          <el-icon><Scale /></el-icon>
          诉讼费计算
        </div>
        <div 
          class="tab-item" 
          :class="{ active: activeCalculator === 'interest' }"
          @click="activeCalculator = 'interest'"
        >
          <el-icon><TrendCharts /></el-icon>
          利息计算
        </div>
        <div 
          class="tab-item" 
          :class="{ active: activeCalculator === 'compensation' }"
          @click="activeCalculator = 'compensation'"
        >
          <el-icon><Wallet /></el-icon>
          赔偿计算
        </div>
      </div>

      <div class="calculator-content">
        <!-- 律师费计算 -->
        <div v-if="activeCalculator === 'lawyer-fee'" class="calculator-panel">
          <div class="form-section">
            <h3>律师费计算</h3>
            <el-form :model="lawyerFeeForm" label-width="120px">
              <el-form-item label="案件类型">
                <el-select v-model="lawyerFeeForm.caseType" placeholder="请选择案件类型">
                  <el-option label="民事案件" value="civil" />
                  <el-option label="刑事案件" value="criminal" />
                  <el-option label="行政案件" value="administrative" />
                  <el-option label="仲裁案件" value="arbitration" />
                </el-select>
              </el-form-item>
              <el-form-item label="标的金额">
                <el-input 
                  v-model="lawyerFeeForm.amount" 
                  placeholder="请输入标的金额（元）"
                  type="number"
                />
              </el-form-item>
              <el-form-item label="收费方式">
                <el-radio-group v-model="lawyerFeeForm.feeType">
                  <el-radio label="proportion">按标的比例</el-radio>
                  <el-radio label="fixed">固定收费</el-radio>
                  <el-radio label="hourly">按小时收费</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="lawyerFeeForm.feeType === 'hourly'" label="预计工时">
                <el-input 
                  v-model="lawyerFeeForm.hours" 
                  placeholder="请输入预计工作小时数"
                  type="number"
                />
              </el-form-item>
              <el-form-item v-if="lawyerFeeForm.feeType === 'hourly'" label="小时费率">
                <el-input 
                  v-model="lawyerFeeForm.hourlyRate" 
                  placeholder="请输入每小时费率（元）"
                  type="number"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="calculateLawyerFee">计算律师费</el-button>
                <el-button @click="resetLawyerFee">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <div class="result-section">
            <h3>计算结果</h3>
            <div v-if="lawyerFeeResult" class="result-card">
              <div class="result-item">
                <label>基础律师费：</label>
                <span class="amount">¥{{ formatNumber(lawyerFeeResult.baseFee) }}</span>
              </div>
              <div class="result-item">
                <label>风险代理费：</label>
                <span class="amount">¥{{ formatNumber(lawyerFeeResult.riskFee) }}</span>
              </div>
              <div class="result-item total">
                <label>总计律师费：</label>
                <span class="amount">¥{{ formatNumber(lawyerFeeResult.totalFee) }}</span>
              </div>
              <div class="result-note">
                <p>* 以上费用仅供参考，实际收费请以律师事务所报价为准</p>
                <p>* 复杂案件可能需要额外收费</p>
              </div>
            </div>
            <div v-else class="empty-result">
              <el-icon><Calculator /></el-icon>
              <p>请填写相关信息后计算</p>
            </div>
          </div>
        </div>

        <!-- 诉讼费计算 -->
        <div v-if="activeCalculator === 'court-fee'" class="calculator-panel">
          <div class="form-section">
            <h3>诉讼费计算</h3>
            <el-form :model="courtFeeForm" label-width="120px">
              <el-form-item label="案件性质">
                <el-select v-model="courtFeeForm.caseNature" placeholder="请选择案件性质">
                  <el-option label="财产案件" value="property" />
                  <el-option label="非财产案件" value="non-property" />
                  <el-option label="知识产权案件" value="ip" />
                  <el-option label="劳动争议案件" value="labor" />
                </el-select>
              </el-form-item>
              <el-form-item v-if="courtFeeForm.caseNature === 'property'" label="争议金额">
                <el-input 
                  v-model="courtFeeForm.disputeAmount" 
                  placeholder="请输入争议金额（元）"
                  type="number"
                />
              </el-form-item>
              <el-form-item label="审级">
                <el-radio-group v-model="courtFeeForm.trialLevel">
                  <el-radio label="first">一审</el-radio>
                  <el-radio label="second">二审</el-radio>
                  <el-radio label="retrial">再审</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="calculateCourtFee">计算诉讼费</el-button>
                <el-button @click="resetCourtFee">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <div class="result-section">
            <h3>计算结果</h3>
            <div v-if="courtFeeResult" class="result-card">
              <div class="result-item">
                <label>案件受理费：</label>
                <span class="amount">¥{{ formatNumber(courtFeeResult.acceptanceFee) }}</span>
              </div>
              <div class="result-item">
                <label>申请费：</label>
                <span class="amount">¥{{ formatNumber(courtFeeResult.applicationFee) }}</span>
              </div>
              <div class="result-item total">
                <label>诉讼费总计：</label>
                <span class="amount">¥{{ formatNumber(courtFeeResult.totalFee) }}</span>
              </div>
              <div class="result-note">
                <p>* 诉讼费用由败诉方承担</p>
                <p>* 部分胜诉的，由双方分担</p>
              </div>
            </div>
            <div v-else class="empty-result">
              <el-icon><Scale /></el-icon>
              <p>请填写相关信息后计算</p>
            </div>
          </div>
        </div>

        <!-- 利息计算 -->
        <div v-if="activeCalculator === 'interest'" class="calculator-panel">
          <div class="form-section">
            <h3>利息计算</h3>
            <el-form :model="interestForm" label-width="120px">
              <el-form-item label="本金金额">
                <el-input 
                  v-model="interestForm.principal" 
                  placeholder="请输入本金金额（元）"
                  type="number"
                />
              </el-form-item>
              <el-form-item label="年利率">
                <el-input 
                  v-model="interestForm.rate" 
                  placeholder="请输入年利率（%）"
                  type="number"
                />
              </el-form-item>
              <el-form-item label="起始日期">
                <el-date-picker
                  v-model="interestForm.startDate"
                  type="date"
                  placeholder="选择起始日期"
                />
              </el-form-item>
              <el-form-item label="结束日期">
                <el-date-picker
                  v-model="interestForm.endDate"
                  type="date"
                  placeholder="选择结束日期"
                />
              </el-form-item>
              <el-form-item label="计息方式">
                <el-radio-group v-model="interestForm.compoundType">
                  <el-radio label="simple">单利</el-radio>
                  <el-radio label="compound">复利</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="calculateInterest">计算利息</el-button>
                <el-button @click="resetInterest">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <div class="result-section">
            <h3>计算结果</h3>
            <div v-if="interestResult" class="result-card">
              <div class="result-item">
                <label>本金：</label>
                <span class="amount">¥{{ formatNumber(interestResult.principal) }}</span>
              </div>
              <div class="result-item">
                <label>利息：</label>
                <span class="amount">¥{{ formatNumber(interestResult.interest) }}</span>
              </div>
              <div class="result-item">
                <label>计息天数：</label>
                <span class="amount">{{ interestResult.days }}天</span>
              </div>
              <div class="result-item total">
                <label>本息合计：</label>
                <span class="amount">¥{{ formatNumber(interestResult.total) }}</span>
              </div>
            </div>
            <div v-else class="empty-result">
              <el-icon><TrendCharts /></el-icon>
              <p>请填写相关信息后计算</p>
            </div>
          </div>
        </div>

        <!-- 赔偿计算 -->
        <div v-if="activeCalculator === 'compensation'" class="calculator-panel">
          <div class="form-section">
            <h3>赔偿计算</h3>
            <el-form :model="compensationForm" label-width="120px">
              <el-form-item label="赔偿类型">
                <el-select v-model="compensationForm.type" placeholder="请选择赔偿类型">
                  <el-option label="人身损害赔偿" value="personal" />
                  <el-option label="财产损失赔偿" value="property" />
                  <el-option label="精神损害赔偿" value="mental" />
                  <el-option label="违约金赔偿" value="breach" />
                </el-select>
              </el-form-item>
              <el-form-item v-if="compensationForm.type === 'personal'" label="医疗费用">
                <el-input 
                  v-model="compensationForm.medicalCost" 
                  placeholder="请输入医疗费用（元）"
                  type="number"
                />
              </el-form-item>
              <el-form-item v-if="compensationForm.type === 'personal'" label="误工天数">
                <el-input 
                  v-model="compensationForm.lostWorkDays" 
                  placeholder="请输入误工天数"
                  type="number"
                />
              </el-form-item>
              <el-form-item v-if="compensationForm.type === 'personal'" label="日工资">
                <el-input 
                  v-model="compensationForm.dailyWage" 
                  placeholder="请输入日工资（元）"
                  type="number"
                />
              </el-form-item>
              <el-form-item v-if="compensationForm.type === 'property'" label="财产损失">
                <el-input 
                  v-model="compensationForm.propertyLoss" 
                  placeholder="请输入财产损失金额（元）"
                  type="number"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="calculateCompensation">计算赔偿</el-button>
                <el-button @click="resetCompensation">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <div class="result-section">
            <h3>计算结果</h3>
            <div v-if="compensationResult" class="result-card">
              <div v-for="item in compensationResult.items" :key="item.name" class="result-item">
                <label>{{ item.name }}：</label>
                <span class="amount">¥{{ formatNumber(item.amount) }}</span>
              </div>
              <div class="result-item total">
                <label>赔偿总计：</label>
                <span class="amount">¥{{ formatNumber(compensationResult.total) }}</span>
              </div>
            </div>
            <div v-else class="empty-result">
              <el-icon><Wallet /></el-icon>
              <p>请填写相关信息后计算</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const activeCalculator = ref('lawyer-fee')

// 律师费表单
const lawyerFeeForm = ref({
  caseType: '',
  amount: '',
  feeType: 'proportion',
  hours: '',
  hourlyRate: ''
})

const lawyerFeeResult = ref<any>(null)

// 诉讼费表单
const courtFeeForm = ref({
  caseNature: '',
  disputeAmount: '',
  trialLevel: 'first'
})

const courtFeeResult = ref<any>(null)

// 利息表单
const interestForm = ref({
  principal: '',
  rate: '',
  startDate: '',
  endDate: '',
  compoundType: 'simple'
})

const interestResult = ref<any>(null)

// 赔偿表单
const compensationForm = ref({
  type: '',
  medicalCost: '',
  lostWorkDays: '',
  dailyWage: '',
  propertyLoss: ''
})

const compensationResult = ref<any>(null)

// 计算律师费
const calculateLawyerFee = () => {
  const amount = parseFloat(lawyerFeeForm.value.amount)
  if (!amount) {
    ElMessage.warning('请输入标的金额')
    return
  }

  let baseFee = 0
  if (lawyerFeeForm.value.feeType === 'proportion') {
    // 按比例计算
    if (amount <= 10000) {
      baseFee = Math.max(amount * 0.08, 1000)
    } else if (amount <= 100000) {
      baseFee = 1000 + (amount - 10000) * 0.06
    } else if (amount <= 1000000) {
      baseFee = 6400 + (amount - 100000) * 0.04
    } else {
      baseFee = 42400 + (amount - 1000000) * 0.02
    }
  } else if (lawyerFeeForm.value.feeType === 'hourly') {
    const hours = parseFloat(lawyerFeeForm.value.hours)
    const rate = parseFloat(lawyerFeeForm.value.hourlyRate)
    baseFee = hours * rate
  } else {
    baseFee = 5000 // 固定收费示例
  }

  const riskFee = baseFee * 0.3 // 风险代理费
  const totalFee = baseFee + riskFee

  lawyerFeeResult.value = {
    baseFee,
    riskFee,
    totalFee
  }

  ElMessage.success('律师费计算完成')
}

// 计算诉讼费
const calculateCourtFee = () => {
  if (courtFeeForm.value.caseNature === 'property') {
    const amount = parseFloat(courtFeeForm.value.disputeAmount)
    if (!amount) {
      ElMessage.warning('请输入争议金额')
      return
    }

    let acceptanceFee = 0
    if (amount <= 10000) {
      acceptanceFee = 50
    } else if (amount <= 100000) {
      acceptanceFee = 50 + (amount - 10000) * 0.025
    } else if (amount <= 200000) {
      acceptanceFee = 2300 + (amount - 100000) * 0.02
    } else {
      acceptanceFee = 4300 + (amount - 200000) * 0.015
    }

    courtFeeResult.value = {
      acceptanceFee,
      applicationFee: 0,
      totalFee: acceptanceFee
    }
  } else {
    // 非财产案件
    courtFeeResult.value = {
      acceptanceFee: 300,
      applicationFee: 0,
      totalFee: 300
    }
  }

  ElMessage.success('诉讼费计算完成')
}

// 计算利息
const calculateInterest = () => {
  const principal = parseFloat(interestForm.value.principal)
  const rate = parseFloat(interestForm.value.rate) / 100
  
  if (!principal || !rate || !interestForm.value.startDate || !interestForm.value.endDate) {
    ElMessage.warning('请填写完整信息')
    return
  }

  const startDate = new Date(interestForm.value.startDate)
  const endDate = new Date(interestForm.value.endDate)
  const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  
  let interest = 0
  if (interestForm.value.compoundType === 'simple') {
    interest = principal * rate * (days / 365)
  } else {
    interest = principal * (Math.pow(1 + rate, days / 365) - 1)
  }

  interestResult.value = {
    principal,
    interest,
    days,
    total: principal + interest
  }

  ElMessage.success('利息计算完成')
}

// 计算赔偿
const calculateCompensation = () => {
  if (!compensationForm.value.type) {
    ElMessage.warning('请选择赔偿类型')
    return
  }

  const items = []
  let total = 0

  if (compensationForm.value.type === 'personal') {
    const medicalCost = parseFloat(compensationForm.value.medicalCost) || 0
    const lostWorkDays = parseFloat(compensationForm.value.lostWorkDays) || 0
    const dailyWage = parseFloat(compensationForm.value.dailyWage) || 0
    const lostWage = lostWorkDays * dailyWage

    items.push({ name: '医疗费', amount: medicalCost })
    items.push({ name: '误工费', amount: lostWage })
    items.push({ name: '护理费', amount: medicalCost * 0.1 })
    items.push({ name: '营养费', amount: medicalCost * 0.05 })
    
    total = medicalCost + lostWage + medicalCost * 0.15
  } else if (compensationForm.value.type === 'property') {
    const propertyLoss = parseFloat(compensationForm.value.propertyLoss) || 0
    items.push({ name: '财产损失', amount: propertyLoss })
    total = propertyLoss
  }

  compensationResult.value = {
    items,
    total
  }

  ElMessage.success('赔偿计算完成')
}

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

// 重置表单
const resetLawyerFee = () => {
  lawyerFeeForm.value = {
    caseType: '',
    amount: '',
    feeType: 'proportion',
    hours: '',
    hourlyRate: ''
  }
  lawyerFeeResult.value = null
}

const resetCourtFee = () => {
  courtFeeForm.value = {
    caseNature: '',
    disputeAmount: '',
    trialLevel: 'first'
  }
  courtFeeResult.value = null
}

const resetInterest = () => {
  interestForm.value = {
    principal: '',
    rate: '',
    startDate: '',
    endDate: '',
    compoundType: 'simple'
  }
  interestResult.value = null
}

const resetCompensation = () => {
  compensationForm.value = {
    type: '',
    medicalCost: '',
    lostWorkDays: '',
    dailyWage: '',
    propertyLoss: ''
  }
  compensationResult.value = null
}
</script>

<style lang="scss" scoped>
.legal-calculator-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .calculator-container {
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    overflow: hidden;

    .calculator-tabs {
      display: flex;
      border-bottom: 1px solid #e4e7ed;
      
      .tab-item {
        flex: 1;
        padding: 16px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        
        &:hover {
          background: #f8f9fa;
        }
        
        &.active {
          background: #409eff;
          color: #fff;
        }
        
        .el-icon {
          font-size: 18px;
        }
      }
    }

    .calculator-content {
      .calculator-panel {
        display: flex;
        min-height: 500px;

        .form-section {
          width: 400px;
          padding: 24px;
          border-right: 1px solid #e4e7ed;
          
          h3 {
            margin: 0 0 20px 0;
            color: #303133;
            font-size: 18px;
          }
        }

        .result-section {
          flex: 1;
          padding: 24px;
          
          h3 {
            margin: 0 0 20px 0;
            color: #303133;
            font-size: 18px;
          }
          
          .result-card {
            .result-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px 0;
              border-bottom: 1px solid #f0f0f0;
              
              &.total {
                border-bottom: none;
                border-top: 2px solid #409eff;
                margin-top: 16px;
                padding-top: 16px;
                font-weight: 600;
                
                .amount {
                  color: #409eff;
                  font-size: 18px;
                }
              }
              
              label {
                color: #606266;
                font-weight: 500;
              }
              
              .amount {
                color: #303133;
                font-weight: 600;
              }
            }
            
            .result-note {
              margin-top: 20px;
              padding: 16px;
              background: #f8f9fa;
              border-radius: 4px;
              
              p {
                margin: 4px 0;
                color: #909399;
                font-size: 12px;
              }
            }
          }
          
          .empty-result {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 300px;
            color: #909399;
            
            .el-icon {
              font-size: 48px;
              margin-bottom: 16px;
            }
            
            p {
              margin: 0;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .legal-calculator-page {
    padding: 15px;
    
    .calculator-container {
      .calculator-tabs {
        flex-wrap: wrap;
        
        .tab-item {
          flex: 1 1 50%;
          min-width: 150px;
        }
      }
      
      .calculator-content .calculator-panel {
        flex-direction: column;
        
        .form-section {
          width: 100%;
          border-right: none;
          border-bottom: 1px solid #e4e7ed;
        }
      }
    }
  }
}
</style>
