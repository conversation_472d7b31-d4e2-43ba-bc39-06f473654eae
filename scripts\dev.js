#!/usr/bin/env node

const { spawn } = require('child_process')
const path = require('path')

console.log('🚀 启动云法务系统开发服务器...\n')

// 检查Node.js版本
const nodeVersion = process.version
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])

if (majorVersion < 16) {
  console.error('❌ 错误: 需要 Node.js 16.0.0 或更高版本')
  console.error(`   当前版本: ${nodeVersion}`)
  process.exit(1)
}

// 启动开发服务器
const dev = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  shell: true,
  cwd: path.resolve(__dirname, '..')
})

dev.on('error', (error) => {
  console.error('❌ 启动失败:', error.message)
  process.exit(1)
})

dev.on('close', (code) => {
  if (code !== 0) {
    console.error(`❌ 开发服务器退出，代码: ${code}`)
    process.exit(code)
  }
})

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n👋 正在关闭开发服务器...')
  dev.kill('SIGINT')
})

process.on('SIGTERM', () => {
  dev.kill('SIGTERM')
})
