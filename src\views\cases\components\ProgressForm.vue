<template>
  <el-dialog
    v-model="dialogVisible"
    title="添加案件进展"
    width="600px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="进展标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入进展标题" />
      </el-form-item>
      
      <el-form-item label="进展内容" prop="content">
        <el-input
          v-model="form.content"
          type="textarea"
          :rows="4"
          placeholder="请输入进展内容"
        />
      </el-form-item>
      
      <el-form-item label="进展时间" prop="progressTime">
        <el-date-picker
          v-model="form.progressTime"
          type="datetime"
          placeholder="请选择进展时间"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  visible: boolean
  caseId: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const form = reactive({
  title: '',
  content: '',
  progressTime: ''
})

const formRules: FormRules = {
  title: [
    { required: true, message: '请输入进展标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入进展内容', trigger: 'blur' }
  ],
  progressTime: [
    { required: true, message: '请选择进展时间', trigger: 'change' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // TODO: 调用添加进展API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('进展添加成功')
    emit('success')
    handleClose()
  } catch (error: any) {
    ElMessage.error(error.message || '添加失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}
</script>
