import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import NProgress from 'nprogress'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', requiresAuth: false }
  },
  {
    path: '/',
    redirect: '/dashboard',
    component: () => import('@/layout/index.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '工作台', icon: 'House' }
      },
      {
        path: 'cases',
        name: 'Cases',
        component: () => import('@/views/cases/index.vue'),
        meta: { title: '案件管理', icon: 'Folder' }
      },
      {
        path: 'cases/detail/:id',
        name: 'CaseDetail',
        component: () => import('@/views/cases/detail.vue'),
        meta: { title: '案件详情', hidden: true }
      },
      {
        path: 'contracts',
        name: 'Contracts',
        component: () => import('@/views/contracts/index.vue'),
        meta: { title: '合同管理', icon: 'Document' }
      },
      {
        path: 'contracts/detail/:id',
        name: 'ContractDetail',
        component: () => import('@/views/contracts/detail.vue'),
        meta: { title: '合同详情', hidden: true }
      },
      {
        path: 'clients',
        name: 'Clients',
        component: () => import('@/views/clients/index.vue'),
        meta: { title: '客户管理', icon: 'User' }
      },
      {
        path: 'documents',
        name: 'Documents',
        component: () => import('@/views/documents/index.vue'),
        meta: { title: '文档管理', icon: 'Document' }
      },
      {
        path: 'finance',
        name: 'Finance',
        component: () => import('@/views/finance/index.vue'),
        meta: { title: '财务管理', icon: 'Money' }
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/views/users/index.vue'),
        meta: { title: '用户管理', icon: 'UserFilled' }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/settings/index.vue'),
        meta: { title: '系统设置', icon: 'Setting' }
      },
      {
        path: 'test',
        name: 'Test',
        component: () => import('@/views/test/index.vue'),
        meta: { title: '路由测试', icon: 'Tools', hidden: true }
      },
      {
        path: 'proxy-test',
        name: 'ProxyTest',
        component: () => import('@/views/test/proxy-test.vue'),
        meta: { title: '代理测试', icon: 'Connection', hidden: true }
      },
      {
        path: 'style-test',
        name: 'StyleTest',
        component: () => import('@/views/style-test/index.vue'),
        meta: { title: '样式测试', icon: 'Brush', hidden: true }
      },
      {
        path: 'ai-consultation',
        name: 'AiConsultation',
        component: () => import('@/views/ai-consultation/index.vue'),
        meta: { title: '智能咨询', icon: 'ChatDotRound', hidden: false }
      },
      {
        path: 'document-drafting',
        name: 'DocumentDrafting',
        component: () => import('@/views/document-drafting/index.vue'),
        meta: { title: '智能文书', icon: 'EditPen', hidden: false }
      },
      {
        path: 'legal-search',
        name: 'LegalSearch',
        component: () => import('@/views/legal-search/index.vue'),
        meta: { title: '法律检索', icon: 'Search', hidden: false }
      },
      {
        path: 'enterprise-info',
        name: 'EnterpriseInfo',
        component: () => import('@/views/enterprise-info/index.vue'),
        meta: { title: '企业信息', icon: 'OfficeBuilding', hidden: false }
      },
      {
        path: 'legal-calculator',
        name: 'LegalCalculator',
        component: () => import('@/views/legal-calculator/index.vue'),
        meta: { title: '法律计算器', icon: 'Calculator', hidden: false }
      },
      {
        path: 'legal-services',
        name: 'LegalServices',
        component: () => import('@/views/legal-services/index.vue'),
        meta: { title: '法务服务', icon: 'Service', hidden: false }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '页面不存在' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  const requiresAuth = to.meta.requiresAuth !== false
  
  if (requiresAuth && !userStore.token) {
    next('/login')
  } else if (to.path === '/login' && userStore.token) {
    next('/')
  } else {
    next()
  }
})

router.afterEach(() => {
  NProgress.done()
})

export default router
