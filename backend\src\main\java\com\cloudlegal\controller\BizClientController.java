package com.cloudlegal.controller;

import com.cloudlegal.common.PageQuery;
import com.cloudlegal.common.PageResult;
import com.cloudlegal.common.Result;
import com.cloudlegal.entity.BizClient;
import com.cloudlegal.service.BizClientService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 客户管理控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Tag(name = "客户管理", description = "客户信息的增删改查等操作")
@RestController
@RequestMapping("/client")
public class BizClientController {

    private final BizClientService clientService;

    public BizClientController(BizClientService clientService) {
        this.clientService = clientService;
    }

    @Operation(summary = "分页查询客户列表", description = "根据条件分页查询客户信息")
    @GetMapping("/page")
    public Result<PageResult<BizClient>> getClientPage(
            @Parameter(description = "分页查询参数") @ModelAttribute PageQuery pageQuery,
            @Parameter(description = "客户类型：1-个人，2-企业，3-组织") @RequestParam(required = false) Integer clientType,
            @Parameter(description = "状态：0-停用，1-正常") @RequestParam(required = false) Integer status) {
        
        PageResult<BizClient> result = clientService.getClientPage(pageQuery, clientType, status);
        return Result.success("查询成功", result);
    }

    @Operation(summary = "查询客户列表", description = "查询所有客户信息")
    @GetMapping("/list")
    public Result<List<BizClient>> getClientList(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "客户类型：1-个人，2-企业，3-组织") @RequestParam(required = false) Integer clientType,
            @Parameter(description = "状态：0-停用，1-正常") @RequestParam(required = false) Integer status) {
        
        List<BizClient> result = clientService.getClientList(keyword, clientType, status);
        return Result.success("查询成功", result);
    }

    @Operation(summary = "根据ID查询客户", description = "根据客户ID查询详细信息")
    @GetMapping("/{id}")
    public Result<BizClient> getClientById(@Parameter(description = "客户ID") @PathVariable Long id) {
        BizClient client = clientService.getById(id);
        if (client == null) {
            return Result.error("客户不存在");
        }
        return Result.success("查询成功", client);
    }

    @Operation(summary = "创建客户", description = "新增客户信息")
    @PostMapping
    public Result<Void> createClient(@Parameter(description = "客户信息") @Valid @RequestBody BizClient client) {
        boolean success = clientService.createClient(client);
        return Result.result(success, success ? "创建成功" : "创建失败");
    }

    @Operation(summary = "更新客户", description = "修改客户信息")
    @PutMapping("/{id}")
    public Result<Void> updateClient(
            @Parameter(description = "客户ID") @PathVariable Long id,
            @Parameter(description = "客户信息") @Valid @RequestBody BizClient client) {
        
        client.setId(id);
        boolean success = clientService.updateClient(client);
        return Result.result(success, success ? "更新成功" : "更新失败");
    }

    @Operation(summary = "删除客户", description = "根据ID删除客户")
    @DeleteMapping("/{id}")
    public Result<Void> deleteClient(@Parameter(description = "客户ID") @PathVariable Long id) {
        boolean success = clientService.deleteClient(id);
        return Result.result(success, success ? "删除成功" : "删除失败");
    }

    @Operation(summary = "批量删除客户", description = "根据ID列表批量删除客户")
    @DeleteMapping("/batch")
    public Result<Void> batchDeleteClients(@Parameter(description = "客户ID列表") @RequestBody List<Long> clientIds) {
        boolean success = clientService.batchDeleteClients(clientIds);
        return Result.result(success, success ? "批量删除成功" : "批量删除失败");
    }

    @Operation(summary = "更新客户状态", description = "启用或禁用客户")
    @PutMapping("/{id}/status")
    public Result<Void> updateClientStatus(
            @Parameter(description = "客户ID") @PathVariable Long id,
            @Parameter(description = "状态：0-停用，1-正常") @RequestParam Integer status) {
        
        boolean success = clientService.updateClientStatus(id, status);
        return Result.result(success, success ? "状态更新成功" : "状态更新失败");
    }

    @Operation(summary = "批量更新客户状态", description = "批量启用或禁用客户")
    @PutMapping("/batch/status")
    public Result<Void> batchUpdateClientStatus(
            @Parameter(description = "客户ID列表") @RequestParam List<Long> clientIds,
            @Parameter(description = "状态：0-停用，1-正常") @RequestParam Integer status) {
        
        boolean success = clientService.batchUpdateClientStatus(clientIds, status);
        return Result.result(success, success ? "批量状态更新成功" : "批量状态更新失败");
    }

    @Operation(summary = "检查客户名称是否存在", description = "验证客户名称的唯一性")
    @GetMapping("/check/name")
    public Result<Boolean> checkClientNameExists(
            @Parameter(description = "客户名称") @RequestParam String clientName,
            @Parameter(description = "排除的客户ID") @RequestParam(required = false) Long excludeId) {
        
        boolean exists = clientService.existsByClientName(clientName, excludeId);
        return Result.success("检查完成", exists);
    }

    @Operation(summary = "检查统一社会信用代码是否存在", description = "验证统一社会信用代码的唯一性")
    @GetMapping("/check/credit-code")
    public Result<Boolean> checkCreditCodeExists(
            @Parameter(description = "统一社会信用代码") @RequestParam String creditCode,
            @Parameter(description = "排除的客户ID") @RequestParam(required = false) Long excludeId) {
        
        boolean exists = clientService.existsByCreditCode(creditCode, excludeId);
        return Result.success("检查完成", exists);
    }

    @Operation(summary = "获取客户统计信息", description = "获取客户相关的统计数据")
    @GetMapping("/stats")
    public Result<Map<String, Object>> getClientStats() {
        Map<String, Object> stats = clientService.getClientStats();
        return Result.success("获取成功", stats);
    }

    @Operation(summary = "按客户类型统计", description = "统计不同类型客户的数量")
    @GetMapping("/stats/type")
    public Result<List<Map<String, Object>>> getClientStatsByType() {
        List<Map<String, Object>> stats = clientService.getClientStatsByType();
        return Result.success("获取成功", stats);
    }

    @Operation(summary = "按行业统计客户", description = "统计不同行业的客户数量")
    @GetMapping("/stats/industry")
    public Result<List<Map<String, Object>>> getClientStatsByIndustry(
            @Parameter(description = "限制返回数量") @RequestParam(defaultValue = "10") Integer limit) {
        
        List<Map<String, Object>> stats = clientService.getClientStatsByIndustry(limit);
        return Result.success("获取成功", stats);
    }

    @Operation(summary = "获取最近创建的客户", description = "获取最近创建的客户列表")
    @GetMapping("/recent")
    public Result<List<BizClient>> getRecentClients(
            @Parameter(description = "限制返回数量") @RequestParam(defaultValue = "5") Integer limit) {
        
        List<BizClient> clients = clientService.getRecentClients(limit);
        return Result.success("获取成功", clients);
    }
}
