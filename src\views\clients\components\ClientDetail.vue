<template>
  <el-dialog
    v-model="dialogVisible"
    title="客户详情"
    width="800px"
    @close="handleClose"
  >
    <div v-loading="loading">
      <el-descriptions v-if="clientData" :column="2" border>
        <el-descriptions-item label="客户名称">{{ clientData.name }}</el-descriptions-item>
        <el-descriptions-item label="客户类型">
          <el-tag :type="clientData.type === 'individual' ? 'primary' : 'success'">
            {{ clientData.type === 'individual' ? '个人' : '企业' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="联系人">{{ clientData.contact }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ clientData.phone }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ clientData.email }}</el-descriptions-item>
        <el-descriptions-item label="行业" v-if="clientData.industry">{{ clientData.industry }}</el-descriptions-item>
        <el-descriptions-item label="地址" :span="2">{{ clientData.address }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="clientData.status === 'active' ? 'success' : 'danger'">
            {{ clientData.status === 'active' ? '正常' : '停用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(clientData.createdAt) }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2" v-if="clientData.description">
          {{ clientData.description }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 相关案件 -->
      <div class="related-section" v-if="relatedCases.length">
        <h4>相关案件</h4>
        <el-table :data="relatedCases" size="small">
          <el-table-column prop="caseNumber" label="案件编号" width="140" />
          <el-table-column prop="title" label="案件标题" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ row.status }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 相关合同 -->
      <div class="related-section" v-if="relatedContracts.length">
        <h4>相关合同</h4>
        <el-table :data="relatedContracts" size="small">
          <el-table-column prop="contractNumber" label="合同编号" width="140" />
          <el-table-column prop="title" label="合同标题" show-overflow-tooltip />
          <el-table-column prop="amount" label="金额" width="120">
            <template #default="{ row }">
              {{ formatMoney(row.amount) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag size="small">{{ row.status }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { getClientDetail } from '@/api/client'
import { formatDate, formatMoney } from '@/utils'
import type { Client } from '@/types'

interface Props {
  visible: boolean
  clientId: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const clientData = ref<Client | null>(null)
const relatedCases = ref<any[]>([])
const relatedContracts = ref<any[]>([])

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 获取客户详情
const fetchClientDetail = async () => {
  if (!props.clientId) return
  
  try {
    loading.value = true
    const data = await getClientDetail(props.clientId)
    clientData.value = data.client
    relatedCases.value = data.relatedCases || []
    relatedContracts.value = data.relatedContracts || []
  } catch (error) {
    console.error('获取客户详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听客户ID变化
watch(
  () => props.clientId,
  (newId) => {
    if (newId && props.visible) {
      fetchClientDetail()
    }
  },
  { immediate: true }
)

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.clientId) {
      fetchClientDetail()
    }
  }
)

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style lang="scss" scoped>
.related-section {
  margin-top: 20px;
  
  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
  }
}
</style>
