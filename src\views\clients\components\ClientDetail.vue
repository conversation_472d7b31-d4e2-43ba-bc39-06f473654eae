<template>
  <el-dialog
    v-model="dialogVisible"
    title="客户详情"
    width="800px"
    @close="handleClose"
  >
    <div v-loading="loading">
      <el-descriptions v-if="clientData" :column="2" border>
        <el-descriptions-item label="客户名称">{{ clientData.clientName }}</el-descriptions-item>
        <el-descriptions-item label="客户类型">
          <el-tag :type="getClientTypeTag(clientData.clientType)">
            {{ getClientTypeText(clientData.clientType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="联系人">{{ clientData.contactPerson }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ clientData.phone }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ clientData.email }}</el-descriptions-item>
        <el-descriptions-item label="行业" v-if="clientData.industry">{{ clientData.industry }}</el-descriptions-item>
        <el-descriptions-item label="地址" :span="2">{{ clientData.address }}</el-descriptions-item>

        <!-- 企业特有信息 -->
        <template v-if="clientData.clientType === 2">
          <el-descriptions-item label="统一社会信用代码" v-if="clientData.creditCode">
            {{ clientData.creditCode }}
          </el-descriptions-item>
          <el-descriptions-item label="法定代表人" v-if="clientData.legalPerson">
            {{ clientData.legalPerson }}
          </el-descriptions-item>
          <el-descriptions-item label="注册资本" v-if="clientData.registeredCapital">
            {{ formatMoney(clientData.registeredCapital) }}
          </el-descriptions-item>
        </template>

        <el-descriptions-item label="状态">
          <el-tag :type="clientData.status === 1 ? 'success' : 'danger'">
            {{ clientData.status === 1 ? '正常' : '停用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(clientData.createdTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDate(clientData.updatedTime) }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2" v-if="clientData.description">
          {{ clientData.description }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 相关案件 -->
      <div class="related-section" v-if="relatedCases.length">
        <h4>相关案件</h4>
        <el-table :data="relatedCases" size="small">
          <el-table-column prop="caseNumber" label="案件编号" width="140" />
          <el-table-column prop="caseTitle" label="案件标题" show-overflow-tooltip />
          <el-table-column prop="caseStatus" label="状态" width="100">
            <template #default="{ row }">
              <el-tag size="small" :type="getCaseStatusTag(row.caseStatus)">
                {{ getCaseStatusText(row.caseStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="caseAmount" label="金额" width="120">
            <template #default="{ row }">
              {{ row.caseAmount ? formatMoney(row.caseAmount) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="createdTime" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createdTime) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 相关合同 -->
      <div class="related-section" v-if="relatedContracts.length">
        <h4>相关合同</h4>
        <el-table :data="relatedContracts" size="small">
          <el-table-column prop="contractNumber" label="合同编号" width="140" />
          <el-table-column prop="contractTitle" label="合同标题" show-overflow-tooltip />
          <el-table-column prop="contractAmount" label="金额" width="120">
            <template #default="{ row }">
              {{ row.contractAmount ? formatMoney(row.contractAmount) : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="contractStatus" label="状态" width="100">
            <template #default="{ row }">
              <el-tag size="small" :type="getContractStatusTag(row.contractStatus)">
                {{ getContractStatusText(row.contractStatus) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="signDate" label="签署日期" width="120">
            <template #default="{ row }">
              {{ row.signDate ? formatDate(row.signDate, 'YYYY-MM-DD') : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="createdTime" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createdTime) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { getClientDetail } from '@/api/client'
import { formatDate, formatMoney } from '@/utils'
import type { Client } from '@/types'

interface Props {
  visible: boolean
  clientId: string | number
}

interface Emits {
  (e: 'update:visible', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)
const clientData = ref<Client | null>(null)
const relatedCases = ref<any[]>([])
const relatedContracts = ref<any[]>([])

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 获取客户类型文本
const getClientTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '个人',
    2: '企业',
    3: '组织'
  }
  return typeMap[type] || '未知'
}

// 获取客户类型标签样式
const getClientTypeTag = (type: number) => {
  const tagMap: Record<number, string> = {
    1: 'primary',
    2: 'success',
    3: 'warning'
  }
  return tagMap[type] || 'info'
}

// 获取案件状态文本
const getCaseStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'closed': '已关闭'
  }
  return statusMap[status] || status
}

// 获取案件状态标签样式
const getCaseStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    'pending': 'warning',
    'processing': 'primary',
    'completed': 'success',
    'closed': 'info'
  }
  return tagMap[status] || 'info'
}

// 获取合同状态文本
const getContractStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'draft': '草稿',
    'reviewing': '审核中',
    'approved': '已批准',
    'signed': '已签署',
    'expired': '已过期'
  }
  return statusMap[status] || status
}

// 获取合同状态标签样式
const getContractStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    'draft': 'info',
    'reviewing': 'warning',
    'approved': 'primary',
    'signed': 'success',
    'expired': 'danger'
  }
  return tagMap[status] || 'info'
}

// 获取客户详情
const fetchClientDetail = async () => {
  if (!props.clientId) return

  try {
    loading.value = true
    const response = await getClientDetail(props.clientId)
    clientData.value = response.data
    relatedCases.value = response.relatedCases || []
    relatedContracts.value = response.relatedContracts || []
  } catch (error) {
    console.error('获取客户详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听客户ID变化
watch(
  () => props.clientId,
  (newId) => {
    if (newId && props.visible) {
      fetchClientDetail()
    }
  },
  { immediate: true }
)

// 监听对话框显示状态
watch(
  () => props.visible,
  (visible) => {
    if (visible && props.clientId) {
      fetchClientDetail()
    }
  }
)

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style lang="scss" scoped>
.related-section {
  margin-top: 20px;
  
  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
  }
}
</style>
