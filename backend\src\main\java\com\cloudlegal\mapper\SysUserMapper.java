package com.cloudlegal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudlegal.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 根据用户名查询用户
     */
    SysUser selectByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     */
    SysUser selectByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户
     */
    SysUser selectByPhone(@Param("phone") String phone);

    /**
     * 分页查询用户列表（包含角色和部门信息）
     */
    IPage<SysUser> selectUserPage(Page<SysUser> page, @Param("keyword") String keyword, 
                                  @Param("status") Integer status, @Param("roleId") Long roleId);

    /**
     * 查询用户列表（包含角色和部门信息）
     */
    List<SysUser> selectUserList(@Param("keyword") String keyword, 
                                 @Param("status") Integer status, @Param("roleId") Long roleId);

    /**
     * 更新用户最后登录信息
     */
    int updateLastLoginInfo(@Param("userId") Long userId, @Param("loginTime") String loginTime, 
                           @Param("loginIp") String loginIp);

    /**
     * 统计用户数量
     */
    Long countUsers(@Param("status") Integer status);

    /**
     * 根据角色ID查询用户数量
     */
    Long countUsersByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据部门ID查询用户列表
     */
    List<SysUser> selectUsersByDeptId(@Param("deptId") Long deptId);

    /**
     * 批量更新用户状态
     */
    int batchUpdateStatus(@Param("userIds") List<Long> userIds, @Param("status") Integer status);
}
