<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑案件' : '新增案件'"
    width="800px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="案件编号" prop="caseNumber">
            <el-input v-model="form.caseNumber" placeholder="请输入案件编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="案件类型" prop="type">
            <el-select v-model="form.type" placeholder="请选择案件类型" style="width: 100%">
              <el-option label="民事诉讼" value="civil" />
              <el-option label="刑事诉讼" value="criminal" />
              <el-option label="行政诉讼" value="administrative" />
              <el-option label="合同纠纷" value="contract" />
              <el-option label="劳动纠纷" value="labor" />
              <el-option label="知识产权" value="intellectual" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="案件标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入案件标题" />
      </el-form-item>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="案件状态" prop="status">
            <el-select v-model="form.status" placeholder="请选择案件状态" style="width: 100%">
              <el-option label="待处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已完成" value="completed" />
              <el-option label="已关闭" value="closed" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="form.priority" placeholder="请选择优先级" style="width: 100%">
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
              <el-option label="紧急" value="urgent" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="客户" prop="clientId">
            <el-select
              v-model="form.clientId"
              placeholder="请选择客户"
              filterable
              remote
              :remote-method="searchClients"
              style="width: 100%"
            >
              <el-option
                v-for="client in clientOptions"
                :key="client.id"
                :label="client.name"
                :value="client.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责律师" prop="lawyerId">
            <el-select
              v-model="form.lawyerId"
              placeholder="请选择负责律师"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="lawyer in lawyerOptions"
                :key="lawyer.id"
                :label="lawyer.realName"
                :value="lawyer.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="案件金额" prop="amount">
            <el-input-number
              v-model="form.amount"
              :min="0"
              :precision="2"
              placeholder="请输入案件金额"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开始日期" prop="startDate">
            <el-date-picker
              v-model="form.startDate"
              type="date"
              placeholder="请选择开始日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="案件描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入案件描述"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { createCase, updateCase } from '@/api/case'
import { getClientList } from '@/api/client'
import { getUserList } from '@/api/user'
import type { Case, Client, User } from '@/types'

interface Props {
  visible: boolean
  formData: Partial<Case>
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const clientOptions = ref<Client[]>([])
const lawyerOptions = ref<User[]>([])

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.formData.id)

const form = reactive<Partial<Case>>({
  caseNumber: '',
  title: '',
  type: '',
  status: 'pending',
  priority: 'medium',
  clientId: '',
  lawyerId: '',
  description: '',
  amount: undefined,
  startDate: ''
})

const formRules: FormRules = {
  caseNumber: [
    { required: true, message: '请输入案件编号', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入案件标题', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择案件类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择案件状态', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  clientId: [
    { required: true, message: '请选择客户', trigger: 'change' }
  ],
  lawyerId: [
    { required: true, message: '请选择负责律师', trigger: 'change' }
  ],
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入案件描述', trigger: 'blur' }
  ]
}

// 监听表单数据变化
watch(
  () => props.formData,
  (newData) => {
    Object.assign(form, {
      caseNumber: '',
      title: '',
      type: '',
      status: 'pending',
      priority: 'medium',
      clientId: '',
      lawyerId: '',
      description: '',
      amount: undefined,
      startDate: '',
      ...newData
    })
  },
  { immediate: true, deep: true }
)

// 搜索客户
const searchClients = async (query: string) => {
  try {
    const data = await getClientList({ keyword: query, size: 20 })
    clientOptions.value = data.list
  } catch (error) {
    console.error('搜索客户失败:', error)
  }
}

// 获取律师列表
const fetchLawyers = async () => {
  try {
    const data = await getUserList({ role: 'lawyer', size: 100 })
    lawyerOptions.value = data.list
  } catch (error) {
    console.error('获取律师列表失败:', error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (isEdit.value) {
      await updateCase(form.id!, form)
      ElMessage.success('更新成功')
    } else {
      await createCase(form)
      ElMessage.success('创建成功')
    }
    
    emit('success')
    handleClose()
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}

onMounted(() => {
  fetchLawyers()
  searchClients('')
})
</script>
