package com.cloudlegal.common;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public enum ResultCode {

    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    SYSTEM_ERROR(500, "系统错误"),
    PARAM_ERROR(400, "参数错误"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    UNSUPPORTED_MEDIA_TYPE(415, "不支持的媒体类型"),

    // 认证授权相关
    UNAUTHORIZED(401, "未认证"),
    FORBIDDEN(403, "无权限"),
    TOKEN_INVALID(4001, "Token无效"),
    TOKEN_EXPIRED(4002, "Token已过期"),
    LOGIN_FAILED(4003, "登录失败"),
    ACCOUNT_DISABLED(4004, "账号已禁用"),
    ACCOUNT_LOCKED(4005, "账号已锁定"),
    PASSWORD_ERROR(4006, "密码错误"),

    // 业务相关
    USER_NOT_FOUND(5001, "用户不存在"),
    USER_ALREADY_EXISTS(5002, "用户已存在"),
    CLIENT_NOT_FOUND(5003, "客户不存在"),
    CASE_NOT_FOUND(5004, "案件不存在"),
    CONTRACT_NOT_FOUND(5005, "合同不存在"),
    DOCUMENT_NOT_FOUND(5006, "文档不存在"),
    
    // 文件相关
    FILE_UPLOAD_ERROR(6001, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(6002, "不支持的文件类型"),
    FILE_SIZE_EXCEEDED(6003, "文件大小超出限制"),
    FILE_NOT_FOUND(6004, "文件不存在"),

    // 数据库相关
    DATABASE_ERROR(7001, "数据库操作失败"),
    DATA_INTEGRITY_VIOLATION(7002, "数据完整性约束违反"),
    DUPLICATE_KEY(7003, "数据重复"),

    // 外部服务相关
    EXTERNAL_SERVICE_ERROR(8001, "外部服务调用失败"),
    NETWORK_ERROR(8002, "网络连接异常"),
    TIMEOUT_ERROR(8003, "请求超时");

    private final Integer code;
    private final String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 根据状态码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return ERROR;
    }
}
