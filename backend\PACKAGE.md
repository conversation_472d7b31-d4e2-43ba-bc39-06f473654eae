# Maven 打包过滤测试文件配置

## 快速使用

### 1. 生产环境打包（推荐）
```bash
# 跳过所有测试，生成生产环境JAR包
mvn clean package -Pprod
```

### 2. 开发环境打包
```bash
# 包含测试，用于开发调试
mvn clean package -Pdev
```

### 3. 使用脚本打包
```bash
# Windows
build.bat

# Linux/Mac
./build.sh
```

## 配置效果

### 过滤的文件类型
- ✅ `*Test.java` - 单元测试类
- ✅ `*Tests.java` - 测试类
- ✅ `*TestCase.java` - 测试用例类
- ✅ `test/` 目录下所有文件
- ✅ `*.test.properties` - 测试配置文件
- ✅ `*.test.yml` - 测试YAML文件

### 保留的文件
- ✅ 主要业务代码
- ✅ 生产环境配置文件
- ✅ 静态资源文件
- ✅ 第三方依赖库

## 验证方法

### 检查JAR包内容
```bash
# 解压查看JAR包内容
jar -tf target/cloud-legal-backend-1.0.0.jar | findstr /i test

# 如果没有输出，说明测试文件已被过滤
```

### 对比文件大小
```bash
# 生产环境打包（无测试）
mvn clean package -Pprod -DskipTests=true
dir target\*.jar

# 开发环境打包（含测试）
mvn clean package -Pdev
dir target\*.jar
```

## 常用命令

```bash
# 只编译，不打包
mvn clean compile -DskipTests=true

# 快速打包（跳过测试和文档）
mvn clean package -DskipTests=true -Dmaven.javadoc.skip=true

# 清理项目
mvn clean

# 查看依赖树
mvn dependency:tree

# 查看有效POM配置
mvn help:effective-pom -Pprod
```

## 注意事项

1. **生产部署**：务必使用 `-Pprod` profile
2. **文件安全**：测试文件可能包含敏感信息，生产环境应排除
3. **包大小**：过滤测试文件可显著减小JAR包大小
4. **构建速度**：跳过测试可大幅提升构建速度

## 故障排除

### Maven命令不识别
```bash
# 检查Maven是否安装
mvn -version

# 如果未安装，请先安装Maven并配置环境变量
```

### Java版本问题
```bash
# 检查Java版本（需要17+）
java -version

# 设置JAVA_HOME环境变量
set JAVA_HOME=C:\Program Files\Java\jdk-17
```

### 依赖下载失败
```bash
# 清理本地仓库缓存
mvn dependency:purge-local-repository

# 强制更新依赖
mvn clean package -U -Pprod -DskipTests=true
```
