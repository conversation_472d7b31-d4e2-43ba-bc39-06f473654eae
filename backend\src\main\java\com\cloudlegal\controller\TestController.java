package com.cloudlegal.controller;

import com.cloudlegal.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Tag(name = "系统测试", description = "系统健康检查和测试接口")
@RestController
@RequestMapping("/test")
public class TestController {

    @Operation(summary = "健康检查", description = "检查系统是否正常运行")
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("application", "cloud-legal-backend");
        data.put("version", "1.0.0");
        
        return Result.success("系统运行正常", data);
    }

    @Operation(summary = "系统信息", description = "获取系统基本信息")
    @GetMapping("/info")
    public Result<Map<String, Object>> info() {
        Map<String, Object> data = new HashMap<>();
        data.put("applicationName", "云法务管理系统");
        data.put("version", "1.0.0");
        data.put("description", "企业级法务管理平台");
        data.put("author", "CloudLegal Team");
        data.put("buildTime", "2024-01-01");
        data.put("javaVersion", System.getProperty("java.version"));
        data.put("osName", System.getProperty("os.name"));
        data.put("osVersion", System.getProperty("os.version"));
        
        return Result.success("获取系统信息成功", data);
    }

    @Operation(summary = "测试数据库连接", description = "测试数据库连接是否正常")
    @GetMapping("/db")
    public Result<String> testDatabase() {
        try {
            // 这里可以添加数据库连接测试逻辑
            return Result.success("数据库连接正常");
        } catch (Exception e) {
            return Result.error("数据库连接失败: " + e.getMessage());
        }
    }

    @Operation(summary = "测试Redis连接", description = "测试Redis连接是否正常")
    @GetMapping("/redis")
    public Result<String> testRedis() {
        try {
            // 这里可以添加Redis连接测试逻辑
            return Result.success("Redis连接正常");
        } catch (Exception e) {
            return Result.error("Redis连接失败: " + e.getMessage());
        }
    }
}
