package com.cloudlegal.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;

/**
 * 客户实体
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Schema(description = "客户实体")
@TableName("biz_client")
public class BizClient extends BaseEntity {

    @Schema(description = "客户名称")
    @NotBlank(message = "客户名称不能为空")
    @Size(max = 100, message = "客户名称长度不能超过100个字符")
    @TableField("client_name")
    private String clientName;

    @Schema(description = "客户类型：1-个人，2-企业，3-组织")
    @NotNull(message = "客户类型不能为空")
    @TableField("client_type")
    private Integer clientType;

    @Schema(description = "联系人")
    @Size(max = 50, message = "联系人长度不能超过50个字符")
    @TableField("contact_person")
    private String contactPerson;

    @Schema(description = "联系电话")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    @TableField("phone")
    private String phone;

    @Schema(description = "邮箱")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    @TableField("email")
    private String email;

    @Schema(description = "地址")
    @Size(max = 255, message = "地址长度不能超过255个字符")
    @TableField("address")
    private String address;

    @Schema(description = "所属行业")
    @Size(max = 50, message = "所属行业长度不能超过50个字符")
    @TableField("industry")
    private String industry;

    @Schema(description = "统一社会信用代码")
    @Size(max = 50, message = "统一社会信用代码长度不能超过50个字符")
    @TableField("credit_code")
    private String creditCode;

    @Schema(description = "法定代表人")
    @Size(max = 50, message = "法定代表人长度不能超过50个字符")
    @TableField("legal_person")
    private String legalPerson;

    @Schema(description = "注册资本")
    @TableField("registered_capital")
    private BigDecimal registeredCapital;

    @Schema(description = "状态：0-停用，1-正常")
    @TableField("status")
    private Integer status;

    @Schema(description = "客户描述")
    @TableField("description")
    private String description;

    // Getter and Setter
    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public Integer getClientType() {
        return clientType;
    }

    public void setClientType(Integer clientType) {
        this.clientType = clientType;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public BigDecimal getRegisteredCapital() {
        return registeredCapital;
    }

    public void setRegisteredCapital(BigDecimal registeredCapital) {
        this.registeredCapital = registeredCapital;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "BizClient{" +
                "clientName='" + clientName + '\'' +
                ", clientType=" + clientType +
                ", contactPerson='" + contactPerson + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", address='" + address + '\'' +
                ", industry='" + industry + '\'' +
                ", creditCode='" + creditCode + '\'' +
                ", legalPerson='" + legalPerson + '\'' +
                ", registeredCapital=" + registeredCapital +
                ", status=" + status +
                ", description='" + description + '\'' +
                "} " + super.toString();
    }
}
