<template>
  <div class="legal-services-page">
    <div class="page-header">
      <h1>法务服务</h1>
      <p>专业律师团队一站式服务</p>
    </div>

    <div class="services-container">
      <!-- 服务分类 -->
      <div class="service-categories">
        <div class="category-tabs">
          <div 
            class="tab-item" 
            :class="{ active: activeCategory === 'litigation' }"
            @click="activeCategory = 'litigation'"
          >
            <el-icon><Scale /></el-icon>
            诉讼代理
          </div>
          <div 
            class="tab-item" 
            :class="{ active: activeCategory === 'corporate' }"
            @click="activeCategory = 'corporate'"
          >
            <el-icon><OfficeBuilding /></el-icon>
            公司法务
          </div>
          <div 
            class="tab-item" 
            :class="{ active: activeCategory === 'contract' }"
            @click="activeCategory = 'contract'"
          >
            <el-icon><Document /></el-icon>
            合同服务
          </div>
          <div 
            class="tab-item" 
            :class="{ active: activeCategory === 'ip' }"
            @click="activeCategory = 'ip'"
          >
            <el-icon><Star /></el-icon>
            知识产权
          </div>
        </div>

        <!-- 服务内容 -->
        <div class="service-content">
          <!-- 诉讼代理 -->
          <div v-if="activeCategory === 'litigation'" class="service-section">
            <div class="service-grid">
              <div class="service-card">
                <div class="service-icon">
                  <el-icon><Scale /></el-icon>
                </div>
                <h3>民事诉讼</h3>
                <p>合同纠纷、侵权纠纷、婚姻家庭等民事案件代理</p>
                <div class="service-features">
                  <span>专业团队</span>
                  <span>胜诉率高</span>
                  <span>费用透明</span>
                </div>
                <div class="service-price">
                  <span class="price">¥5,000起</span>
                  <el-button type="primary" size="small" @click="consultService('民事诉讼')">
                    立即咨询
                  </el-button>
                </div>
              </div>

              <div class="service-card">
                <div class="service-icon">
                  <el-icon><Warning /></el-icon>
                </div>
                <h3>刑事辩护</h3>
                <p>刑事案件辩护、取保候审、减刑假释等服务</p>
                <div class="service-features">
                  <span>经验丰富</span>
                  <span>24小时响应</span>
                  <span>保密严格</span>
                </div>
                <div class="service-price">
                  <span class="price">¥10,000起</span>
                  <el-button type="primary" size="small" @click="consultService('刑事辩护')">
                    立即咨询
                  </el-button>
                </div>
              </div>

              <div class="service-card">
                <div class="service-icon">
                  <el-icon><Management /></el-icon>
                </div>
                <h3>行政诉讼</h3>
                <p>行政复议、行政诉讼、政府法律顾问服务</p>
                <div class="service-features">
                  <span>政策熟悉</span>
                  <span>关系良好</span>
                  <span>成功率高</span>
                </div>
                <div class="service-price">
                  <span class="price">¥8,000起</span>
                  <el-button type="primary" size="small" @click="consultService('行政诉讼')">
                    立即咨询
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 公司法务 -->
          <div v-if="activeCategory === 'corporate'" class="service-section">
            <div class="service-grid">
              <div class="service-card">
                <div class="service-icon">
                  <el-icon><OfficeBuilding /></el-icon>
                </div>
                <h3>公司设立</h3>
                <p>公司注册、章程制定、股权设计等服务</p>
                <div class="service-features">
                  <span>一站式服务</span>
                  <span>快速办理</span>
                  <span>合规保障</span>
                </div>
                <div class="service-price">
                  <span class="price">¥3,000起</span>
                  <el-button type="primary" size="small" @click="consultService('公司设立')">
                    立即咨询
                  </el-button>
                </div>
              </div>

              <div class="service-card">
                <div class="service-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <h3>股权投资</h3>
                <p>股权转让、投资并购、尽职调查等服务</p>
                <div class="service-features">
                  <span>专业团队</span>
                  <span>风险控制</span>
                  <span>价值最大化</span>
                </div>
                <div class="service-price">
                  <span class="price">¥20,000起</span>
                  <el-button type="primary" size="small" @click="consultService('股权投资')">
                    立即咨询
                  </el-button>
                </div>
              </div>

              <div class="service-card">
                <div class="service-icon">
                  <el-icon><User /></el-icon>
                </div>
                <h3>劳动法务</h3>
                <p>劳动合同、员工手册、劳动争议处理</p>
                <div class="service-features">
                  <span>制度完善</span>
                  <span>风险防范</span>
                  <span>争议解决</span>
                </div>
                <div class="service-price">
                  <span class="price">¥2,000起</span>
                  <el-button type="primary" size="small" @click="consultService('劳动法务')">
                    立即咨询
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 合同服务 -->
          <div v-if="activeCategory === 'contract'" class="service-section">
            <div class="service-grid">
              <div class="service-card">
                <div class="service-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <h3>合同起草</h3>
                <p>各类合同起草、条款设计、风险防范</p>
                <div class="service-features">
                  <span>专业起草</span>
                  <span>风险识别</span>
                  <span>条款优化</span>
                </div>
                <div class="service-price">
                  <span class="price">¥1,500起</span>
                  <el-button type="primary" size="small" @click="consultService('合同起草')">
                    立即咨询
                  </el-button>
                </div>
              </div>

              <div class="service-card">
                <div class="service-icon">
                  <el-icon><View /></el-icon>
                </div>
                <h3>合同审查</h3>
                <p>合同条款审查、风险评估、修改建议</p>
                <div class="service-features">
                  <span>专业审查</span>
                  <span>风险提示</span>
                  <span>修改建议</span>
                </div>
                <div class="service-price">
                  <span class="price">¥800起</span>
                  <el-button type="primary" size="small" @click="consultService('合同审查')">
                    立即咨询
                  </el-button>
                </div>
              </div>

              <div class="service-card">
                <div class="service-icon">
                  <el-icon><Warning /></el-icon>
                </div>
                <h3>合同纠纷</h3>
                <p>合同违约处理、争议调解、诉讼代理</p>
                <div class="service-features">
                  <span>快速响应</span>
                  <span>专业处理</span>
                  <span>维权到底</span>
                </div>
                <div class="service-price">
                  <span class="price">¥3,000起</span>
                  <el-button type="primary" size="small" @click="consultService('合同纠纷')">
                    立即咨询
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 知识产权 -->
          <div v-if="activeCategory === 'ip'" class="service-section">
            <div class="service-grid">
              <div class="service-card">
                <div class="service-icon">
                  <el-icon><Star /></el-icon>
                </div>
                <h3>商标服务</h3>
                <p>商标注册、商标转让、商标维权等服务</p>
                <div class="service-features">
                  <span>快速注册</span>
                  <span>成功率高</span>
                  <span>全程跟踪</span>
                </div>
                <div class="service-price">
                  <span class="price">¥1,200起</span>
                  <el-button type="primary" size="small" @click="consultService('商标服务')">
                    立即咨询
                  </el-button>
                </div>
              </div>

              <div class="service-card">
                <div class="service-icon">
                  <el-icon><Cpu /></el-icon>
                </div>
                <h3>专利服务</h3>
                <p>专利申请、专利检索、专利维权等服务</p>
                <div class="service-features">
                  <span>技术专业</span>
                  <span>申请快速</span>
                  <span>维权有力</span>
                </div>
                <div class="service-price">
                  <span class="price">¥3,000起</span>
                  <el-button type="primary" size="small" @click="consultService('专利服务')">
                    立即咨询
                  </el-button>
                </div>
              </div>

              <div class="service-card">
                <div class="service-icon">
                  <el-icon><Reading /></el-icon>
                </div>
                <h3>版权服务</h3>
                <p>著作权登记、版权保护、侵权维权</p>
                <div class="service-features">
                  <span>登记便捷</span>
                  <span>保护全面</span>
                  <span>维权专业</span>
                </div>
                <div class="service-price">
                  <span class="price">¥800起</span>
                  <el-button type="primary" size="small" @click="consultService('版权服务')">
                    立即咨询
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 律师团队 -->
      <div class="lawyer-team">
        <h2>专业律师团队</h2>
        <div class="lawyer-grid">
          <div v-for="lawyer in lawyers" :key="lawyer.id" class="lawyer-card">
            <div class="lawyer-avatar">
              <img :src="lawyer.avatar" :alt="lawyer.name" />
            </div>
            <div class="lawyer-info">
              <h3>{{ lawyer.name }}</h3>
              <p class="lawyer-title">{{ lawyer.title }}</p>
              <p class="lawyer-specialty">专长：{{ lawyer.specialty }}</p>
              <div class="lawyer-stats">
                <span>执业{{ lawyer.experience }}年</span>
                <span>成功案例{{ lawyer.cases }}+</span>
              </div>
              <el-button type="primary" size="small" @click="consultLawyer(lawyer)">
                预约咨询
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 服务流程 -->
      <div class="service-process">
        <h2>服务流程</h2>
        <div class="process-steps">
          <div class="step-item">
            <div class="step-number">1</div>
            <h3>需求咨询</h3>
            <p>详细了解您的法律需求</p>
          </div>
          <div class="step-item">
            <div class="step-number">2</div>
            <h3>方案制定</h3>
            <p>制定专业的解决方案</p>
          </div>
          <div class="step-item">
            <div class="step-number">3</div>
            <h3>签约服务</h3>
            <p>签署服务协议，开始服务</p>
          </div>
          <div class="step-item">
            <div class="step-number">4</div>
            <h3>跟踪反馈</h3>
            <p>全程跟踪，及时反馈进展</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 咨询对话框 -->
    <el-dialog v-model="consultDialogVisible" title="服务咨询" width="500px">
      <el-form :model="consultForm" label-width="80px">
        <el-form-item label="服务类型">
          <el-input v-model="consultForm.service" readonly />
        </el-form-item>
        <el-form-item label="联系人">
          <el-input v-model="consultForm.name" placeholder="请输入您的姓名" />
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="consultForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="需求描述">
          <el-input 
            v-model="consultForm.description" 
            type="textarea" 
            :rows="4"
            placeholder="请详细描述您的法律需求"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="consultDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitConsult">提交咨询</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const activeCategory = ref('litigation')
const consultDialogVisible = ref(false)

// 咨询表单
const consultForm = ref({
  service: '',
  name: '',
  phone: '',
  description: ''
})

// 律师团队数据
const lawyers = [
  {
    id: '1',
    name: '张律师',
    title: '高级合伙人',
    specialty: '公司法、合同法',
    experience: 15,
    cases: 500,
    avatar: 'https://via.placeholder.com/80x80'
  },
  {
    id: '2',
    name: '李律师',
    title: '资深律师',
    specialty: '知识产权、民事诉讼',
    experience: 12,
    cases: 300,
    avatar: 'https://via.placeholder.com/80x80'
  },
  {
    id: '3',
    name: '王律师',
    title: '专业律师',
    specialty: '刑事辩护、行政诉讼',
    experience: 8,
    cases: 200,
    avatar: 'https://via.placeholder.com/80x80'
  },
  {
    id: '4',
    name: '赵律师',
    title: '专业律师',
    specialty: '劳动法、婚姻家庭',
    experience: 10,
    cases: 250,
    avatar: 'https://via.placeholder.com/80x80'
  }
]

// 咨询服务
const consultService = (service: string) => {
  consultForm.value.service = service
  consultDialogVisible.value = true
}

// 咨询律师
const consultLawyer = (lawyer: any) => {
  consultForm.value.service = `${lawyer.name}律师咨询`
  consultDialogVisible.value = true
}

// 提交咨询
const submitConsult = () => {
  if (!consultForm.value.name || !consultForm.value.phone) {
    ElMessage.warning('请填写联系人和电话')
    return
  }

  ElMessage.success('咨询提交成功，我们会尽快联系您！')
  consultDialogVisible.value = false
  
  // 重置表单
  consultForm.value = {
    service: '',
    name: '',
    phone: '',
    description: ''
  }
}
</script>

<style lang="scss" scoped>
.legal-services-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    h1 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 28px;
    }
    
    p {
      margin: 0;
      color: #606266;
      font-size: 16px;
    }
  }

  .services-container {
    .service-categories {
      background: #fff;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      margin-bottom: 40px;
      overflow: hidden;

      .category-tabs {
        display: flex;
        border-bottom: 1px solid #e4e7ed;
        
        .tab-item {
          flex: 1;
          padding: 20px;
          text-align: center;
          cursor: pointer;
          transition: all 0.3s;
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          
          &:hover {
            background: #f8f9fa;
          }
          
          &.active {
            background: #409eff;
            color: #fff;
          }
          
          .el-icon {
            font-size: 24px;
          }
        }
      }

      .service-content {
        padding: 30px;
        
        .service-section {
          .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            
            .service-card {
              border: 1px solid #e4e7ed;
              border-radius: 8px;
              padding: 24px;
              text-align: center;
              transition: all 0.3s;
              
              &:hover {
                border-color: #409eff;
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
                transform: translateY(-2px);
              }
              
              .service-icon {
                width: 60px;
                height: 60px;
                margin: 0 auto 16px;
                background: #f0f9ff;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                
                .el-icon {
                  font-size: 28px;
                  color: #409eff;
                }
              }
              
              h3 {
                margin: 0 0 12px 0;
                color: #303133;
                font-size: 18px;
              }
              
              p {
                margin: 0 0 16px 0;
                color: #606266;
                line-height: 1.5;
              }
              
              .service-features {
                display: flex;
                justify-content: center;
                gap: 8px;
                margin-bottom: 20px;
                
                span {
                  padding: 4px 8px;
                  background: #f0f9ff;
                  color: #409eff;
                  border-radius: 4px;
                  font-size: 12px;
                }
              }
              
              .service-price {
                display: flex;
                justify-content: space-between;
                align-items: center;
                
                .price {
                  font-size: 18px;
                  font-weight: 600;
                  color: #f56c6c;
                }
              }
            }
          }
        }
      }
    }

    .lawyer-team {
      margin-bottom: 40px;
      
      h2 {
        text-align: center;
        margin: 0 0 30px 0;
        color: #303133;
        font-size: 24px;
      }
      
      .lawyer-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 24px;
        
        .lawyer-card {
          background: #fff;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          padding: 24px;
          text-align: center;
          transition: all 0.3s;
          
          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
          }
          
          .lawyer-avatar {
            margin-bottom: 16px;
            
            img {
              width: 80px;
              height: 80px;
              border-radius: 50%;
              object-fit: cover;
            }
          }
          
          .lawyer-info {
            h3 {
              margin: 0 0 8px 0;
              color: #303133;
              font-size: 18px;
            }
            
            .lawyer-title {
              margin: 0 0 8px 0;
              color: #409eff;
              font-weight: 500;
            }
            
            .lawyer-specialty {
              margin: 0 0 12px 0;
              color: #606266;
              font-size: 14px;
            }
            
            .lawyer-stats {
              margin-bottom: 16px;
              
              span {
                display: inline-block;
                margin: 0 8px;
                padding: 4px 8px;
                background: #f8f9fa;
                border-radius: 4px;
                font-size: 12px;
                color: #909399;
              }
            }
          }
        }
      }
    }

    .service-process {
      background: #fff;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
      padding: 40px;
      
      h2 {
        text-align: center;
        margin: 0 0 40px 0;
        color: #303133;
        font-size: 24px;
      }
      
      .process-steps {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 30px;
        
        .step-item {
          text-align: center;
          
          .step-number {
            width: 50px;
            height: 50px;
            margin: 0 auto 16px;
            background: #409eff;
            color: #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: 600;
          }
          
          h3 {
            margin: 0 0 8px 0;
            color: #303133;
            font-size: 16px;
          }
          
          p {
            margin: 0;
            color: #606266;
            font-size: 14px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .legal-services-page {
    padding: 15px;
    
    .services-container {
      .service-categories .category-tabs {
        flex-wrap: wrap;
        
        .tab-item {
          flex: 1 1 50%;
          min-width: 120px;
        }
      }
      
      .lawyer-team .lawyer-grid {
        grid-template-columns: 1fr;
      }
      
      .service-process .process-steps {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
