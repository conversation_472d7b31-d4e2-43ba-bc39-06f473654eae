# CORS 跨域问题解决指南

## 问题现象

当您看到以下错误信息时，说明遇到了CORS跨域问题：

```
Invalid CORS request
Access to XMLHttpRequest at 'http://localhost:8080/api/...' from origin 'http://localhost:8090' has been blocked by CORS policy
```

## 快速解决方案

### 1. 检查服务状态

```bash
# 确保前端服务运行在8090端口
npm run dev

# 确保后端服务运行在8080端口
cd backend && mvn spring-boot:run
```

### 2. 运行CORS诊断工具

```bash
# 安装依赖（如果需要）
npm install axios colors

# 运行诊断脚本
node scripts/cors-diagnosis.js
```

### 3. 使用CORS测试页面

访问前端测试页面：`http://localhost:8090/#/test/cors-test`

## 配置检查清单

### ✅ 后端配置检查

#### 1. SecurityConfig.java
```java
// 确保包含前端端口
configuration.setAllowedOrigins(Arrays.asList(
    "http://localhost:8090",  // ← 确保包含这个
    "http://localhost:3000",
    "http://localhost:5173"
));
```

#### 2. CorsConfig.java
```java
// 确保配置正确
config.setAllowedOrigins(Arrays.asList(
    "http://localhost:8090"  // ← 前端地址
));
config.setAllowCredentials(true);
config.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
```

#### 3. application.yml
```yaml
app:
  cors:
    allowed-origins:
      - http://localhost:8090  # ← 确保包含前端地址
```

### ✅ 前端配置检查

#### 1. vite.config.ts
```typescript
server: {
  port: 8090,  // ← 确保端口正确
  proxy: {
    "/api": {
      target: 'http://localhost:8080',  // ← 后端地址
      changeOrigin: true,
      secure: false
    }
  }
}
```

#### 2. .env.development
```env
VITE_API_BASE_URL=/api  # ← 使用代理路径
```

#### 3. request.ts
```typescript
const service = axios.create({
  baseURL: '/api',  // ← 使用代理路径
  timeout: 10000
})
```

## 常见问题及解决方案

### 问题1: "Invalid CORS request"

**原因**: 后端CORS配置不包含前端地址

**解决方案**:
1. 检查 `SecurityConfig.java` 中的 `allowedOrigins`
2. 确保包含 `http://localhost:8090`
3. 重启后端服务

### 问题2: "Access-Control-Allow-Origin" 头缺失

**原因**: CORS配置未生效或配置错误

**解决方案**:
1. 检查是否有多个CORS配置冲突
2. 确保 `CorsConfig.java` 配置正确
3. 检查 `@CrossOrigin` 注解是否正确

### 问题3: OPTIONS预检请求失败

**原因**: 预检请求配置不正确

**解决方案**:
1. 确保允许 `OPTIONS` 方法
2. 检查 `Access-Control-Allow-Headers` 配置
3. 确保 `maxAge` 设置合理

### 问题4: 凭证(Credentials)问题

**原因**: `allowCredentials` 配置不一致

**解决方案**:
1. 后端设置 `setAllowCredentials(true)`
2. 前端axios配置 `withCredentials: true`
3. 不能同时使用 `allowedOrigins("*")` 和 `allowCredentials(true)`

## 调试步骤

### 1. 浏览器开发者工具

1. 打开 Network 面板
2. 查看失败的请求
3. 检查 Request Headers 和 Response Headers
4. 特别关注 CORS 相关的头信息

### 2. 后端日志

```yaml
# 在 application.yml 中启用调试日志
logging:
  level:
    org.springframework.web.cors: DEBUG
    org.springframework.security: DEBUG
```

### 3. 前端代理日志

在 `vite.config.ts` 中启用代理日志：

```typescript
proxy: {
  "/api": {
    target: 'http://localhost:8080',
    changeOrigin: true,
    configure: (proxy, options) => {
      proxy.on('proxyReq', (proxyReq, req, res) => {
        console.log('Proxy Request:', req.method, req.url);
      });
    }
  }
}
```

## 测试验证

### 1. 手动测试

```bash
# 测试OPTIONS预检请求
curl -X OPTIONS \
  -H "Origin: http://localhost:8090" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type" \
  http://localhost:8080/api/test/cors/post

# 测试GET请求
curl -X GET \
  -H "Origin: http://localhost:8090" \
  http://localhost:8080/api/test/cors/get
```

### 2. 使用测试页面

访问 `http://localhost:8090/#/test/cors-test` 进行全面测试

### 3. 运行诊断脚本

```bash
node scripts/cors-diagnosis.js
```

## 生产环境注意事项

1. **不要使用通配符**: 生产环境不要使用 `allowedOrigins("*")`
2. **指定具体域名**: 只允许真实的前端域名
3. **HTTPS支持**: 确保HTTPS环境下的CORS配置
4. **安全头配置**: 配置适当的安全响应头

```java
// 生产环境配置示例
configuration.setAllowedOrigins(Arrays.asList(
    "https://your-frontend-domain.com",
    "https://www.your-frontend-domain.com"
));
```

## 联系支持

如果按照以上步骤仍无法解决问题，请提供：

1. 浏览器开发者工具的Network截图
2. 后端服务日志
3. 前端和后端的具体配置
4. 错误信息的完整堆栈

这将帮助我们更快地定位和解决问题。
