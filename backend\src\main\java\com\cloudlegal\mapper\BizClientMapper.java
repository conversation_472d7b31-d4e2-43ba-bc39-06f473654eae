package com.cloudlegal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudlegal.entity.BizClient;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 客户Mapper接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface BizClientMapper extends BaseMapper<BizClient> {

    /**
     * 分页查询客户列表
     */
    IPage<BizClient> selectClientPage(Page<BizClient> page, @Param("keyword") String keyword,
                                      @Param("clientType") Integer clientType, @Param("status") Integer status);

    /**
     * 查询客户列表
     */
    List<BizClient> selectClientList(@Param("keyword") String keyword,
                                     @Param("clientType") Integer clientType, @Param("status") Integer status);

    /**
     * 根据客户名称查询客户
     */
    BizClient selectByClientName(@Param("clientName") String clientName);

    /**
     * 根据统一社会信用代码查询客户
     */
    BizClient selectByCreditCode(@Param("creditCode") String creditCode);

    /**
     * 统计客户数量
     */
    Long countClients(@Param("status") Integer status);

    /**
     * 按客户类型统计数量
     */
    List<Map<String, Object>> countByClientType();

    /**
     * 按行业统计客户数量
     */
    List<Map<String, Object>> countByIndustry(@Param("limit") Integer limit);

    /**
     * 查询最近创建的客户
     */
    List<BizClient> selectRecentClients(@Param("limit") Integer limit);

    /**
     * 批量更新客户状态
     */
    int batchUpdateStatus(@Param("clientIds") List<Long> clientIds, @Param("status") Integer status);

    /**
     * 查询客户统计信息
     */
    Map<String, Object> selectClientStats();
}
