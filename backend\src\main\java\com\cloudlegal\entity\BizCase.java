package com.cloudlegal.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 案件实体
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Schema(description = "案件实体")
@TableName("biz_case")
public class BizCase extends BaseEntity {

    @Schema(description = "案件编号")
    @NotBlank(message = "案件编号不能为空")
    @Size(max = 50, message = "案件编号长度不能超过50个字符")
    @TableField("case_number")
    private String caseNumber;

    @Schema(description = "案件标题")
    @NotBlank(message = "案件标题不能为空")
    @Size(max = 200, message = "案件标题长度不能超过200个字符")
    @TableField("case_title")
    private String caseTitle;

    @Schema(description = "案件类型")
    @NotBlank(message = "案件类型不能为空")
    @Size(max = 50, message = "案件类型长度不能超过50个字符")
    @TableField("case_type")
    private String caseType;

    @Schema(description = "案件状态")
    @NotBlank(message = "案件状态不能为空")
    @Size(max = 20, message = "案件状态长度不能超过20个字符")
    @TableField("case_status")
    private String caseStatus;

    @Schema(description = "优先级")
    @NotBlank(message = "优先级不能为空")
    @Size(max = 20, message = "优先级长度不能超过20个字符")
    @TableField("priority_level")
    private String priorityLevel;

    @Schema(description = "客户ID")
    @NotNull(message = "客户ID不能为空")
    @TableField("client_id")
    private Long clientId;

    @Schema(description = "客户名称")
    @Size(max = 100, message = "客户名称长度不能超过100个字符")
    @TableField("client_name")
    private String clientName;

    @Schema(description = "负责律师ID")
    @TableField("lawyer_id")
    private Long lawyerId;

    @Schema(description = "负责律师姓名")
    @Size(max = 50, message = "负责律师姓名长度不能超过50个字符")
    @TableField("lawyer_name")
    private String lawyerName;

    @Schema(description = "案件金额")
    @TableField("case_amount")
    private BigDecimal caseAmount;

    @Schema(description = "开始日期")
    @TableField("start_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @TableField("end_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @Schema(description = "审理法院")
    @Size(max = 100, message = "审理法院长度不能超过100个字符")
    @TableField("court_name")
    private String courtName;

    @Schema(description = "案件描述")
    @TableField("case_description")
    private String caseDescription;

    // Getter and Setter
    public String getCaseNumber() {
        return caseNumber;
    }

    public void setCaseNumber(String caseNumber) {
        this.caseNumber = caseNumber;
    }

    public String getCaseTitle() {
        return caseTitle;
    }

    public void setCaseTitle(String caseTitle) {
        this.caseTitle = caseTitle;
    }

    public String getCaseType() {
        return caseType;
    }

    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }

    public String getCaseStatus() {
        return caseStatus;
    }

    public void setCaseStatus(String caseStatus) {
        this.caseStatus = caseStatus;
    }

    public String getPriorityLevel() {
        return priorityLevel;
    }

    public void setPriorityLevel(String priorityLevel) {
        this.priorityLevel = priorityLevel;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public Long getLawyerId() {
        return lawyerId;
    }

    public void setLawyerId(Long lawyerId) {
        this.lawyerId = lawyerId;
    }

    public String getLawyerName() {
        return lawyerName;
    }

    public void setLawyerName(String lawyerName) {
        this.lawyerName = lawyerName;
    }

    public BigDecimal getCaseAmount() {
        return caseAmount;
    }

    public void setCaseAmount(BigDecimal caseAmount) {
        this.caseAmount = caseAmount;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getCourtName() {
        return courtName;
    }

    public void setCourtName(String courtName) {
        this.courtName = courtName;
    }

    public String getCaseDescription() {
        return caseDescription;
    }

    public void setCaseDescription(String caseDescription) {
        this.caseDescription = caseDescription;
    }

    @Override
    public String toString() {
        return "BizCase{" +
                "caseNumber='" + caseNumber + '\'' +
                ", caseTitle='" + caseTitle + '\'' +
                ", caseType='" + caseType + '\'' +
                ", caseStatus='" + caseStatus + '\'' +
                ", priorityLevel='" + priorityLevel + '\'' +
                ", clientId=" + clientId +
                ", clientName='" + clientName + '\'' +
                ", lawyerId=" + lawyerId +
                ", lawyerName='" + lawyerName + '\'' +
                ", caseAmount=" + caseAmount +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", courtName='" + courtName + '\'' +
                ", caseDescription='" + caseDescription + '\'' +
                "} " + super.toString();
    }
}
