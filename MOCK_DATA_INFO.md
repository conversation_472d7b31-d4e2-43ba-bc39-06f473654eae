# 🎯 Mock数据说明

## 📊 已添加的测试数据

### 👥 用户数据 (5个用户)
- **admin** - 系统管理员 (所有权限)
- **lawyer** - 张律师 (法务权限)
- **assistant** - 李助理 (助理权限)  
- **user** - 王用户 (只读权限)
- **lawyer2** - 赵律师 (法务权限)

### 📁 案件数据 (4个案件)
1. **某科技公司知识产权纠纷案** - 处理中/高优先级
2. **房地产买卖合同纠纷** - 待处理/中优先级
3. **劳动争议仲裁案** - 已完成/低优先级
4. **公司股权转让纠纷** - 处理中/紧急

### 📄 合同数据 (3个合同)
1. **软件开发服务合同** - 已签署
2. **法律顾问服务协议** - 已批准
3. **办公设备采购合同** - 审核中

### 👤 客户数据 (5个客户)
1. **北京科技有限公司** - 企业客户
2. **李先生** - 个人客户
3. **王女士** - 个人客户
4. **上海投资集团** - 企业客户
5. **某设备供应商** - 企业客户

### 📎 文档数据 (4个文档)
1. **合同模板-服务协议.docx** - Word文档
2. **案件证据材料.pdf** - PDF文档
3. **财务报表.xlsx** - Excel文档
4. **客户资料照片.jpg** - 图片文档

### 💰 财务记录 (5条记录)
- 收入记录：律师费、咨询费
- 支出记录：办公费用、差旅费
- 总收入：145,000元
- 总支出：11,500元
- 净利润：133,500元

## 🔧 功能特性

### ✅ 完整的CRUD操作
- 创建、读取、更新、删除
- 分页和搜索功能
- 状态筛选和分类

### ✅ 数据关联
- 案件与客户关联
- 合同与客户关联
- 文档与案件/合同关联
- 财务记录与案件/合同关联

### ✅ 模拟网络延迟
- 所有API调用都有300ms延迟
- 模拟真实网络环境

### ✅ 错误处理
- 数据不存在时返回错误
- 参数验证
- 友好的错误提示

## 🎮 体验建议

### 1. 登录系统
使用 `admin/123456` 获得完整权限

### 2. 浏览数据
- **工作台** - 查看统计数据和图表
- **案件管理** - 查看案件列表和详情
- **合同管理** - 管理合同和模板
- **客户管理** - 查看客户信息和关联数据
- **文档管理** - 文档上传和预览
- **财务管理** - 收支统计和图表

### 3. 测试功能
- **搜索功能** - 在各个列表页面搜索
- **筛选功能** - 按状态、类型等筛选
- **分页功能** - 测试分页导航
- **CRUD操作** - 创建、编辑、删除数据

### 4. 数据操作
- **新增数据** - 创建新的案件、合同、客户等
- **编辑数据** - 修改现有数据
- **删除数据** - 删除不需要的数据
- **状态更新** - 更改案件、合同状态

## 📝 数据持久化

⚠️ **注意**: 当前Mock数据存储在内存中，页面刷新后会重置为初始状态。

如需数据持久化，可以：
1. 使用localStorage存储
2. 连接真实后端API
3. 使用IndexedDB本地数据库

## 🔄 切换到真实API

要切换到真实后端API，只需：

1. 修改 `.env` 文件：
```env
VITE_ENABLE_MOCK=false
VITE_API_BASE_URL=http://your-api-server.com
```

2. 重启开发服务器：
```bash
npm run dev
```

## 🐛 问题排查

如果遇到"列表获取失败"错误：

1. **检查控制台** - 查看具体错误信息
2. **确认Mock模式** - 确保 `VITE_ENABLE_MOCK=true`
3. **重启服务器** - `npm run dev`
4. **清除缓存** - 删除 `node_modules/.vite` 文件夹

## 📞 技术支持

如果仍有问题，请检查：
- Node.js版本 >= 16.0.0
- 浏览器控制台错误信息
- 网络连接状态
