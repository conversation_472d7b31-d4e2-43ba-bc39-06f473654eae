@echo off
chcp 65001 >nul
echo ========================================
echo CORS 环境配置切换工具
echo ========================================

echo.
echo 当前CORS配置状态：
echo 🔓 开发环境 - 允许所有源访问
echo.

echo 请选择要切换的环境：
echo [1] 保持开发环境配置（允许所有源）
echo [2] 切换到生产环境配置（限制特定域名）
echo [3] 查看当前配置
echo [4] 安全风险说明
echo [0] 退出
echo.

set /p choice=请输入选择 (0-4): 

if "%choice%"=="1" goto dev_config
if "%choice%"=="2" goto prod_config  
if "%choice%"=="3" goto show_config
if "%choice%"=="4" goto security_info
if "%choice%"=="0" goto exit
goto invalid_choice

:dev_config
echo.
echo 🔧 配置为开发环境...
echo.
echo ✅ 当前已是开发环境配置
echo    - 允许所有源访问 (*)
echo    - 适用于本地开发和测试
echo.
echo ⚠️  警告：此配置不适用于生产环境！
echo    详细安全说明请查看：docs/CORS_SECURITY_WARNING.md
goto end

:prod_config
echo.
echo 🔒 切换到生产环境配置...
echo.
set /p domain=请输入生产环境域名 (例如: https://your-domain.com): 

if "%domain%"=="" (
    echo ❌ 域名不能为空
    goto prod_config
)

echo.
echo 正在更新配置文件...

:: 备份原始文件
copy "backend\src\main\java\com\cloudlegal\config\SecurityConfig.java" "backend\src\main\java\com\cloudlegal\config\SecurityConfig.java.bak" >nul 2>&1
copy "backend\src\main\java\com\cloudlegal\config\CorsConfig.java" "backend\src\main\java\com\cloudlegal\config\CorsConfig.java.bak" >nul 2>&1
copy "backend\src\main\resources\application.yml" "backend\src\main\resources\application.yml.bak" >nul 2>&1

echo ✅ 已备份原始配置文件
echo.
echo 📝 需要手动更新以下文件：
echo.
echo 1. backend/src/main/java/com/cloudlegal/config/SecurityConfig.java
echo    将: configuration.setAllowedOriginPatterns(Collections.singletonList("*"));
echo    改为: configuration.setAllowedOrigins(Arrays.asList("%domain%"));
echo.
echo 2. backend/src/main/java/com/cloudlegal/config/CorsConfig.java  
echo    将: config.setAllowedOriginPatterns(Collections.singletonList("*"));
echo    改为: config.setAllowedOrigins(Arrays.asList("%domain%"));
echo.
echo 3. backend/src/main/resources/application.yml
echo    将: - "*"
echo    改为: - %domain%
echo.
echo ⚠️  请手动完成以上修改，然后重启后端服务
goto end

:show_config
echo.
echo 📋 当前CORS配置：
echo.
echo 🔍 SecurityConfig.java:
findstr /C:"setAllowedOrigin" "backend\src\main\java\com\cloudlegal\config\SecurityConfig.java" 2>nul
echo.
echo 🔍 CorsConfig.java:
findstr /C:"setAllowedOrigin" "backend\src\main\java\com\cloudlegal\config\CorsConfig.java" 2>nul
echo.
echo 🔍 application.yml:
findstr /A /C:"allowed-origins" "backend\src\main\resources\application.yml" 2>nul
goto end

:security_info
echo.
echo 🚨 CORS安全风险说明
echo ========================================
echo.
echo 当前配置（允许所有源）的风险：
echo.
echo 1. 跨站请求伪造 (CSRF)
echo    - 恶意网站可以向您的API发送请求
echo    - 用户可能在不知情下执行敏感操作
echo.
echo 2. 数据泄露
echo    - 任何网站都可以读取API响应
echo    - 敏感信息可能被恶意获取
echo.
echo 3. 身份验证绕过
echo    - 恶意网站可能利用用户登录状态
echo    - 执行未授权操作
echo.
echo 📖 详细说明请查看：docs/CORS_SECURITY_WARNING.md
echo.
echo 🔒 生产环境建议：
echo    - 只允许特定的前端域名
echo    - 使用HTTPS协议
echo    - 定期审查CORS配置
goto end

:invalid_choice
echo.
echo ❌ 无效选择，请输入 0-4 之间的数字
echo.
goto :eof

:end
echo.
echo ========================================
pause
goto :eof

:exit
echo.
echo 👋 再见！
echo.
