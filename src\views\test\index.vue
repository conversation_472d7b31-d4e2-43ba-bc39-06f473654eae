<template>
  <div class="test-page">
    <h1>路由测试页面</h1>
    <p>当前路由: {{ $route.path }}</p>
    <p>当前时间: {{ currentTime }}</p>
    
    <div class="route-links">
      <h3>测试所有路由:</h3>
      <div class="link-grid">
        <router-link to="/dashboard" class="test-link">工作台</router-link>
        <router-link to="/cases" class="test-link">案件管理</router-link>
        <router-link to="/contracts" class="test-link">合同管理</router-link>
        <router-link to="/clients" class="test-link">客户管理</router-link>
        <router-link to="/documents" class="test-link">文档管理</router-link>
        <router-link to="/finance" class="test-link">财务管理</router-link>
        <router-link to="/users" class="test-link">用户管理</router-link>
        <router-link to="/settings" class="test-link">系统设置</router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const currentTime = ref('')

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

let timer: NodeJS.Timeout

onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20px;
  
  h1 {
    color: #409eff;
    margin-bottom: 20px;
  }
  
  .route-links {
    margin-top: 30px;
    
    h3 {
      margin-bottom: 15px;
    }
    
    .link-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 10px;
      
      .test-link {
        display: block;
        padding: 10px 15px;
        background: #f0f9ff;
        border: 1px solid #409eff;
        border-radius: 4px;
        text-decoration: none;
        color: #409eff;
        text-align: center;
        transition: all 0.3s;
        
        &:hover {
          background: #409eff;
          color: white;
        }
        
        &.router-link-active {
          background: #409eff;
          color: white;
        }
      }
    }
  }
}
</style>
