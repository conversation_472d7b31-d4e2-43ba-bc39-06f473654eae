import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 简化的路由配置用于调试
const debugRoutes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', requiresAuth: false }
  },
  {
    path: '/',
    redirect: '/dashboard',
    component: () => import('@/layout/index.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '工作台', icon: 'House' }
      },
      {
        path: 'test',
        name: 'Test',
        component: () => import('@/views/test/index.vue'),
        meta: { title: '路由测试', icon: 'Tools' }
      },
      {
        path: 'test/cors-test',
        name: 'CorsTest',
        component: () => import('@/views/test/cors-test.vue'),
        meta: { title: 'CORS测试', icon: 'Connection' }
      },
      {
        path: 'test/proxy-test',
        name: 'ProxyTest',
        component: () => import('@/views/test/proxy-test.vue'),
        meta: { title: '代理测试', icon: 'Link' }
      }
    ]
  }
]

export const createDebugRouter = () => {
  return createRouter({
    history: createWebHistory(),
    routes: debugRoutes
  })
}

export { debugRoutes }
