// 模拟数据生成工具
export const generateId = () => Math.random().toString(36).substr(2, 9)

export const generateDate = (daysAgo = 0) => {
  const date = new Date()
  date.setDate(date.getDate() - daysAgo)
  return date.toISOString()
}

export const randomChoice = <T>(arr: T[]): T => {
  return arr[Math.floor(Math.random() * arr.length)]
}

export const randomNumber = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// 模拟案件数据
export const mockCases = [
  {
    id: '1',
    caseNumber: 'CASE2024001',
    title: '某科技公司知识产权纠纷案',
    type: 'intellectual',
    status: 'processing',
    priority: 'high',
    clientId: '1',
    clientName: '北京科技有限公司',
    lawyerId: '2',
    lawyerName: '张律师',
    description: '涉及专利侵权纠纷，需要进行专利无效宣告程序',
    amount: 500000,
    startDate: generateDate(30),
    endDate: null,
    createdAt: generateDate(30),
    updatedAt: generateDate(1)
  },
  {
    id: '2',
    caseNumber: 'CASE2024002',
    title: '房地产买卖合同纠纷',
    type: 'contract',
    status: 'pending',
    priority: 'medium',
    clientId: '2',
    clientName: '李先生',
    lawyerId: '2',
    lawyerName: '张律师',
    description: '购房合同违约，要求退房退款',
    amount: 200000,
    startDate: generateDate(15),
    endDate: null,
    createdAt: generateDate(15),
    updatedAt: generateDate(2)
  },
  {
    id: '3',
    caseNumber: 'CASE2024003',
    title: '劳动争议仲裁案',
    type: 'labor',
    status: 'completed',
    priority: 'low',
    clientId: '3',
    clientName: '王女士',
    lawyerId: '3',
    lawyerName: '李助理',
    description: '工伤赔偿纠纷，已达成调解协议',
    amount: 80000,
    startDate: generateDate(60),
    endDate: generateDate(5),
    createdAt: generateDate(60),
    updatedAt: generateDate(5)
  },
  {
    id: '4',
    caseNumber: 'CASE2024004',
    title: '公司股权转让纠纷',
    type: 'corporate',
    status: 'processing',
    priority: 'urgent',
    clientId: '4',
    clientName: '上海投资集团',
    lawyerId: '2',
    lawyerName: '张律师',
    description: '股权转让协议争议，涉及估值分歧',
    amount: 1200000,
    startDate: generateDate(20),
    endDate: null,
    createdAt: generateDate(20),
    updatedAt: generateDate(1)
  },
  {
    id: '5',
    caseNumber: 'CASE2024005',
    title: '商标侵权诉讼案',
    type: 'intellectual',
    status: 'processing',
    priority: 'high',
    clientId: '6',
    clientName: '深圳创新科技',
    lawyerId: '5',
    lawyerName: '赵律师',
    description: '竞争对手恶意抢注商标，要求撤销并赔偿损失',
    amount: 350000,
    startDate: generateDate(25),
    endDate: null,
    createdAt: generateDate(25),
    updatedAt: generateDate(3)
  },
  {
    id: '6',
    caseNumber: 'CASE2024006',
    title: '建设工程合同纠纷',
    type: 'contract',
    status: 'pending',
    priority: 'medium',
    clientId: '7',
    clientName: '广州建筑集团',
    lawyerId: '2',
    lawyerName: '张律师',
    description: '工程款支付争议，涉及工程质量问题',
    amount: 800000,
    startDate: generateDate(10),
    endDate: null,
    createdAt: generateDate(10),
    updatedAt: generateDate(1)
  },
  {
    id: '7',
    caseNumber: 'CASE2024007',
    title: '离婚财产分割案',
    type: 'civil',
    status: 'processing',
    priority: 'medium',
    clientId: '8',
    clientName: '陈女士',
    lawyerId: '3',
    lawyerName: '李助理',
    description: '夫妻共同财产分割，涉及房产和股权',
    amount: 150000,
    startDate: generateDate(40),
    endDate: null,
    createdAt: generateDate(40),
    updatedAt: generateDate(5)
  },
  {
    id: '8',
    caseNumber: 'CASE2024008',
    title: '网络诽谤名誉权纠纷',
    type: 'civil',
    status: 'completed',
    priority: 'low',
    clientId: '9',
    clientName: '某知名演员',
    lawyerId: '5',
    lawyerName: '赵律师',
    description: '网络恶意传播不实信息，损害名誉权',
    amount: 120000,
    startDate: generateDate(80),
    endDate: generateDate(10),
    createdAt: generateDate(80),
    updatedAt: generateDate(10)
  },
  {
    id: '9',
    caseNumber: 'CASE2024009',
    title: '公司并购法律服务',
    type: 'corporate',
    status: 'processing',
    priority: 'urgent',
    clientId: '10',
    clientName: '杭州投资基金',
    lawyerId: '2',
    lawyerName: '张律师',
    description: '协助完成目标公司收购，进行法律尽职调查',
    amount: 2000000,
    startDate: generateDate(5),
    endDate: null,
    createdAt: generateDate(5),
    updatedAt: generateDate(1)
  },
  {
    id: '10',
    caseNumber: 'CASE2024010',
    title: '产品质量纠纷案',
    type: 'consumer',
    status: 'pending',
    priority: 'medium',
    clientId: '11',
    clientName: '消费者协会',
    lawyerId: '3',
    lawyerName: '李助理',
    description: '批量产品质量问题，消费者集体维权',
    amount: 300000,
    startDate: generateDate(8),
    endDate: null,
    createdAt: generateDate(8),
    updatedAt: generateDate(2)
  }
]

// 模拟合同数据
export const mockContracts = [
  {
    id: '1',
    contractNumber: 'CT2024001',
    title: '软件开发服务合同',
    type: 'service',
    status: 'signed',
    clientId: '1',
    clientName: '北京科技有限公司',
    amount: 300000,
    signDate: generateDate(10),
    startDate: generateDate(10),
    endDate: generateDate(-90),
    createdAt: generateDate(20),
    updatedAt: generateDate(10)
  },
  {
    id: '2',
    contractNumber: 'CT2024002',
    title: '法律顾问服务协议',
    type: 'service',
    status: 'approved',
    clientId: '4',
    clientName: '上海投资集团',
    amount: 150000,
    signDate: null,
    startDate: generateDate(5),
    endDate: generateDate(-360),
    createdAt: generateDate(15),
    updatedAt: generateDate(3)
  },
  {
    id: '3',
    contractNumber: '*********',
    title: '办公设备采购合同',
    type: 'purchase',
    status: 'reviewing',
    clientId: '5',
    clientName: '某设备供应商',
    amount: 80000,
    signDate: null,
    startDate: generateDate(0),
    endDate: generateDate(-30),
    createdAt: generateDate(5),
    updatedAt: generateDate(1)
  },
  {
    id: '4',
    contractNumber: '*********',
    title: '知识产权许可协议',
    type: 'license',
    status: 'signed',
    clientId: '6',
    clientName: '深圳创新科技',
    amount: 500000,
    signDate: generateDate(20),
    startDate: generateDate(20),
    endDate: generateDate(-365),
    createdAt: generateDate(30),
    updatedAt: generateDate(20)
  },
  {
    id: '5',
    contractNumber: '*********',
    title: '建设工程施工合同',
    type: 'construction',
    status: 'signed',
    clientId: '7',
    clientName: '广州建筑集团',
    amount: 2500000,
    signDate: generateDate(45),
    startDate: generateDate(45),
    endDate: generateDate(-180),
    createdAt: generateDate(50),
    updatedAt: generateDate(45)
  },
  {
    id: '6',
    contractNumber: '*********',
    title: '投资咨询服务合同',
    type: 'consulting',
    status: 'draft',
    clientId: '10',
    clientName: '杭州投资基金',
    amount: 800000,
    signDate: null,
    startDate: generateDate(-30),
    endDate: generateDate(-365),
    createdAt: generateDate(3),
    updatedAt: generateDate(1)
  },
  {
    id: '7',
    contractNumber: 'CT2024007',
    title: '品牌代理合作协议',
    type: 'agency',
    status: 'approved',
    clientId: '12',
    clientName: '时尚品牌公司',
    amount: 600000,
    signDate: null,
    startDate: generateDate(-15),
    endDate: generateDate(-365),
    createdAt: generateDate(12),
    updatedAt: generateDate(2)
  },
  {
    id: '8',
    contractNumber: 'CT2024008',
    title: '技术转让合同',
    type: 'transfer',
    status: 'reviewing',
    clientId: '13',
    clientName: '新能源科技',
    amount: 1200000,
    signDate: null,
    startDate: generateDate(-10),
    endDate: generateDate(-730),
    createdAt: generateDate(7),
    updatedAt: generateDate(1)
  },
  {
    id: '9',
    contractNumber: 'CT2024009',
    title: '供应链管理服务合同',
    type: 'service',
    status: 'signed',
    clientId: '14',
    clientName: '物流集团',
    amount: 450000,
    signDate: generateDate(35),
    startDate: generateDate(35),
    endDate: generateDate(-365),
    createdAt: generateDate(40),
    updatedAt: generateDate(35)
  },
  {
    id: '10',
    contractNumber: 'CT2024010',
    title: '数据处理服务协议',
    type: 'service',
    status: 'expired',
    clientId: '15',
    clientName: '大数据公司',
    amount: 320000,
    signDate: generateDate(400),
    startDate: generateDate(400),
    endDate: generateDate(35),
    createdAt: generateDate(410),
    updatedAt: generateDate(35)
  }
]

// 模拟客户数据
export const mockClients = [
  {
    id: '1',
    name: '北京科技有限公司',
    type: 'company',
    contact: '张总',
    phone: '13800138001',
    email: '<EMAIL>',
    address: '北京市海淀区中关村大街1号',
    industry: '软件开发',
    status: 'active',
    description: '专注于人工智能技术研发的高新技术企业',
    createdAt: generateDate(100),
    updatedAt: generateDate(10)
  },
  {
    id: '2',
    name: '李先生',
    type: 'individual',
    contact: '李先生',
    phone: '13800138002',
    email: '<EMAIL>',
    address: '上海市浦东新区陆家嘴金融区',
    industry: '',
    status: 'active',
    description: '房地产投资客户',
    createdAt: generateDate(80),
    updatedAt: generateDate(15)
  },
  {
    id: '3',
    name: '王女士',
    type: 'individual',
    contact: '王女士',
    phone: '13800138003',
    email: '<EMAIL>',
    address: '广州市天河区珠江新城',
    industry: '',
    status: 'active',
    description: '劳动纠纷当事人',
    createdAt: generateDate(70),
    updatedAt: generateDate(5)
  },
  {
    id: '4',
    name: '上海投资集团',
    type: 'company',
    contact: '刘总监',
    phone: '13800138004',
    email: '<EMAIL>',
    address: '上海市黄浦区南京东路100号',
    industry: '投资管理',
    status: 'active',
    description: '大型投资管理公司，业务涵盖股权投资、并购重组等',
    createdAt: generateDate(120),
    updatedAt: generateDate(20)
  },
  {
    id: '5',
    name: '某设备供应商',
    type: 'company',
    contact: '陈经理',
    phone: '13800138005',
    email: '<EMAIL>',
    address: '深圳市南山区科技园',
    industry: '设备制造',
    status: 'active',
    description: '专业办公设备供应商',
    createdAt: generateDate(50),
    updatedAt: generateDate(5)
  },
  {
    id: '6',
    name: '深圳创新科技',
    type: 'company',
    contact: '赵总经理',
    phone: '13800138006',
    email: '<EMAIL>',
    address: '深圳市南山区高新技术产业园',
    industry: '科技创新',
    status: 'active',
    description: '专注于新兴技术研发和产业化应用的创新型企业',
    createdAt: generateDate(90),
    updatedAt: generateDate(25)
  },
  {
    id: '7',
    name: '广州建筑集团',
    type: 'company',
    contact: '孙项目经理',
    phone: '13800138007',
    email: '<EMAIL>',
    address: '广州市天河区建设大道888号',
    industry: '建筑工程',
    status: 'active',
    description: '大型建筑工程承包商，具有一级建筑资质',
    createdAt: generateDate(150),
    updatedAt: generateDate(45)
  },
  {
    id: '8',
    name: '陈女士',
    type: 'individual',
    contact: '陈女士',
    phone: '13800138008',
    email: '<EMAIL>',
    address: '杭州市西湖区文三路',
    industry: '',
    status: 'active',
    description: '离婚案件当事人，涉及财产分割',
    createdAt: generateDate(45),
    updatedAt: generateDate(5)
  },
  {
    id: '9',
    name: '某知名演员',
    type: 'individual',
    contact: '经纪人小王',
    phone: '13800138009',
    email: '<EMAIL>',
    address: '北京市朝阳区CBD核心区',
    industry: '娱乐影视',
    status: 'active',
    description: '知名影视演员，涉及名誉权纠纷案件',
    createdAt: generateDate(85),
    updatedAt: generateDate(10)
  },
  {
    id: '10',
    name: '杭州投资基金',
    type: 'company',
    contact: '基金经理李总',
    phone: '13800138010',
    email: '<EMAIL>',
    address: '杭州市滨江区网商路699号',
    industry: '金融投资',
    status: 'active',
    description: '专业投资基金管理公司，专注于科技企业投资',
    createdAt: generateDate(60),
    updatedAt: generateDate(5)
  },
  {
    id: '11',
    name: '消费者协会',
    type: 'organization',
    contact: '维权部主任',
    phone: '13800138011',
    email: '<EMAIL>',
    address: '北京市东城区消费者权益保护大厦',
    industry: '消费者权益保护',
    status: 'active',
    description: '消费者权益保护组织，代理消费者集体维权',
    createdAt: generateDate(200),
    updatedAt: generateDate(8)
  },
  {
    id: '12',
    name: '时尚品牌公司',
    type: 'company',
    contact: '品牌总监',
    phone: '13800138012',
    email: '<EMAIL>',
    address: '上海市静安区南京西路时尚大厦',
    industry: '时尚零售',
    status: 'active',
    description: '国际知名时尚品牌中国区运营公司',
    createdAt: generateDate(75),
    updatedAt: generateDate(12)
  },
  {
    id: '13',
    name: '新能源科技',
    type: 'company',
    contact: '技术总监',
    phone: '13800138013',
    email: '<EMAIL>',
    address: '西安市高新区新能源产业园',
    industry: '新能源',
    status: 'active',
    description: '新能源技术研发和设备制造企业',
    createdAt: generateDate(40),
    updatedAt: generateDate(7)
  },
  {
    id: '14',
    name: '物流集团',
    type: 'company',
    contact: '运营总监',
    phone: '13800138014',
    email: '<EMAIL>',
    address: '天津市滨海新区物流园区',
    industry: '物流运输',
    status: 'active',
    description: '全国性物流服务提供商，覆盖供应链全流程',
    createdAt: generateDate(110),
    updatedAt: generateDate(35)
  },
  {
    id: '15',
    name: '大数据公司',
    type: 'company',
    contact: '数据总监',
    phone: '13800138015',
    email: '<EMAIL>',
    address: '成都市高新区天府软件园',
    industry: '大数据',
    status: 'inactive',
    description: '大数据分析和处理服务提供商，合同已到期',
    createdAt: generateDate(450),
    updatedAt: generateDate(35)
  }
]

// 模拟文档数据
export const mockDocuments = [
  {
    id: '1',
    name: '合同模板-服务协议.docx',
    type: 'docx',
    size: 1024000,
    category: '合同模板',
    uploaderName: '张律师',
    caseTitle: '某科技公司知识产权纠纷案',
    contractTitle: '软件开发服务合同',
    url: '/mock/documents/contract-template.docx',
    createdAt: generateDate(30)
  },
  {
    id: '2',
    name: '案件证据材料.pdf',
    type: 'pdf',
    size: 2048000,
    category: '案件材料',
    uploaderName: '李助理',
    caseTitle: '房地产买卖合同纠纷',
    contractTitle: '',
    url: '/mock/documents/evidence.pdf',
    createdAt: generateDate(20)
  },
  {
    id: '3',
    name: '财务报表.xlsx',
    type: 'xlsx',
    size: 512000,
    category: '财务文档',
    uploaderName: '系统管理员',
    caseTitle: '',
    contractTitle: '',
    url: '/mock/documents/financial-report.xlsx',
    createdAt: generateDate(10)
  },
  {
    id: '4',
    name: '客户资料照片.jpg',
    type: 'jpg',
    size: 256000,
    category: '客户资料',
    uploaderName: '张律师',
    caseTitle: '',
    contractTitle: '',
    url: '/mock/documents/client-photo.jpg',
    createdAt: generateDate(5)
  },
  {
    id: '5',
    name: '专利申请书.pdf',
    type: 'pdf',
    size: 1536000,
    category: '案件材料',
    uploaderName: '赵律师',
    caseTitle: '商标侵权诉讼案',
    contractTitle: '',
    url: '/mock/documents/patent-application.pdf',
    createdAt: generateDate(25)
  },
  {
    id: '6',
    name: '建设工程施工图纸.dwg',
    type: 'dwg',
    size: 5120000,
    category: '案件材料',
    uploaderName: '张律师',
    caseTitle: '建设工程合同纠纷',
    contractTitle: '建设工程施工合同',
    url: '/mock/documents/construction-plan.dwg',
    createdAt: generateDate(15)
  },
  {
    id: '7',
    name: '离婚协议书模板.docx',
    type: 'docx',
    size: 768000,
    category: '合同模板',
    uploaderName: '李助理',
    caseTitle: '离婚财产分割案',
    contractTitle: '',
    url: '/mock/documents/divorce-agreement.docx',
    createdAt: generateDate(40)
  },
  {
    id: '8',
    name: '网络截图证据.png',
    type: 'png',
    size: 1280000,
    category: '案件材料',
    uploaderName: '赵律师',
    caseTitle: '网络诽谤名誉权纠纷',
    contractTitle: '',
    url: '/mock/documents/web-evidence.png',
    createdAt: generateDate(80)
  },
  {
    id: '9',
    name: '尽职调查报告.pdf',
    type: 'pdf',
    size: 3072000,
    category: '法律文书',
    uploaderName: '张律师',
    caseTitle: '公司并购法律服务',
    contractTitle: '投资咨询服务合同',
    url: '/mock/documents/due-diligence.pdf',
    createdAt: generateDate(5)
  },
  {
    id: '10',
    name: '产品质量检测报告.pdf',
    type: 'pdf',
    size: 2560000,
    category: '案件材料',
    uploaderName: '李助理',
    caseTitle: '产品质量纠纷案',
    contractTitle: '',
    url: '/mock/documents/quality-report.pdf',
    createdAt: generateDate(8)
  },
  {
    id: '11',
    name: '知识产权许可协议.docx',
    type: 'docx',
    size: 896000,
    category: '合同模板',
    uploaderName: '赵律师',
    caseTitle: '',
    contractTitle: '知识产权许可协议',
    url: '/mock/documents/ip-license.docx',
    createdAt: generateDate(30)
  },
  {
    id: '12',
    name: '法院判决书.pdf',
    type: 'pdf',
    size: 1792000,
    category: '法律文书',
    uploaderName: '李助理',
    caseTitle: '劳动争议仲裁案',
    contractTitle: '',
    url: '/mock/documents/court-judgment.pdf',
    createdAt: generateDate(60)
  },
  {
    id: '13',
    name: '品牌授权书.pdf',
    type: 'pdf',
    size: 640000,
    category: '客户资料',
    uploaderName: '张律师',
    caseTitle: '',
    contractTitle: '品牌代理合作协议',
    url: '/mock/documents/brand-authorization.pdf',
    createdAt: generateDate(12)
  },
  {
    id: '14',
    name: '技术转让评估报告.xlsx',
    type: 'xlsx',
    size: 1024000,
    category: '财务文档',
    uploaderName: '系统管理员',
    caseTitle: '',
    contractTitle: '技术转让合同',
    url: '/mock/documents/tech-valuation.xlsx',
    createdAt: generateDate(7)
  },
  {
    id: '15',
    name: '供应链流程图.pptx',
    type: 'pptx',
    size: 2048000,
    category: '客户资料',
    uploaderName: '赵律师',
    caseTitle: '',
    contractTitle: '供应链管理服务合同',
    url: '/mock/documents/supply-chain.pptx',
    createdAt: generateDate(40)
  }
]

// 模拟财务记录数据
export const mockFinanceRecords = [
  {
    id: '1',
    type: 'income',
    category: 'lawyer_fee',
    amount: 50000,
    description: '某科技公司知识产权纠纷案律师费',
    date: generateDate(10),
    caseId: '1',
    caseTitle: '某科技公司知识产权纠纷案',
    contractId: '',
    contractTitle: '',
    createdAt: generateDate(10)
  },
  {
    id: '2',
    type: 'income',
    category: 'consulting_fee',
    amount: 15000,
    description: '法律咨询服务费',
    date: generateDate(15),
    caseId: '',
    caseTitle: '',
    contractId: '2',
    contractTitle: '法律顾问服务协议',
    createdAt: generateDate(15)
  },
  {
    id: '3',
    type: 'expense',
    category: 'office_expense',
    amount: 8000,
    description: '办公用品采购',
    date: generateDate(20),
    caseId: '',
    caseTitle: '',
    contractId: '',
    contractTitle: '',
    createdAt: generateDate(20)
  },
  {
    id: '4',
    type: 'expense',
    category: 'travel_expense',
    amount: 3500,
    description: '出差调研费用',
    date: generateDate(25),
    caseId: '2',
    caseTitle: '房地产买卖合同纠纷',
    contractId: '',
    contractTitle: '',
    createdAt: generateDate(25)
  },
  {
    id: '5',
    type: 'income',
    category: 'lawyer_fee',
    amount: 80000,
    description: '公司股权转让纠纷案件费用',
    date: generateDate(5),
    caseId: '4',
    caseTitle: '公司股权转让纠纷',
    contractId: '',
    contractTitle: '',
    createdAt: generateDate(5)
  },
  {
    id: '6',
    type: 'income',
    category: 'lawyer_fee',
    amount: 35000,
    description: '商标侵权诉讼案代理费',
    date: generateDate(25),
    caseId: '5',
    caseTitle: '商标侵权诉讼案',
    contractId: '',
    contractTitle: '',
    createdAt: generateDate(25)
  },
  {
    id: '7',
    type: 'income',
    category: 'consulting_fee',
    amount: 120000,
    description: '建设工程法律服务费',
    date: generateDate(45),
    caseId: '6',
    caseTitle: '建设工程合同纠纷',
    contractId: '5',
    contractTitle: '建设工程施工合同',
    createdAt: generateDate(45)
  },
  {
    id: '8',
    type: 'expense',
    category: 'court_fee',
    amount: 5000,
    description: '离婚案件诉讼费',
    date: generateDate(40),
    caseId: '7',
    caseTitle: '离婚财产分割案',
    contractId: '',
    contractTitle: '',
    createdAt: generateDate(40)
  },
  {
    id: '9',
    type: 'income',
    category: 'lawyer_fee',
    amount: 25000,
    description: '名誉权纠纷案件费用',
    date: generateDate(80),
    caseId: '8',
    caseTitle: '网络诽谤名誉权纠纷',
    contractId: '',
    contractTitle: '',
    createdAt: generateDate(80)
  },
  {
    id: '10',
    type: 'income',
    category: 'consulting_fee',
    amount: 200000,
    description: '公司并购法律服务费',
    date: generateDate(5),
    caseId: '9',
    caseTitle: '公司并购法律服务',
    contractId: '6',
    contractTitle: '投资咨询服务合同',
    createdAt: generateDate(5)
  },
  {
    id: '11',
    type: 'expense',
    category: 'expert_fee',
    amount: 12000,
    description: '产品质量专家鉴定费',
    date: generateDate(8),
    caseId: '10',
    caseTitle: '产品质量纠纷案',
    contractId: '',
    contractTitle: '',
    createdAt: generateDate(8)
  },
  {
    id: '12',
    type: 'income',
    category: 'license_fee',
    amount: 60000,
    description: '知识产权许可协议服务费',
    date: generateDate(20),
    caseId: '',
    caseTitle: '',
    contractId: '4',
    contractTitle: '知识产权许可协议',
    createdAt: generateDate(20)
  },
  {
    id: '13',
    type: 'expense',
    category: 'office_expense',
    amount: 15000,
    description: '办公设备维护费',
    date: generateDate(30),
    caseId: '',
    caseTitle: '',
    contractId: '',
    contractTitle: '',
    createdAt: generateDate(30)
  },
  {
    id: '14',
    type: 'income',
    category: 'consulting_fee',
    amount: 45000,
    description: '品牌代理法律咨询费',
    date: generateDate(12),
    caseId: '',
    caseTitle: '',
    contractId: '7',
    contractTitle: '品牌代理合作协议',
    createdAt: generateDate(12)
  },
  {
    id: '15',
    type: 'expense',
    category: 'travel_expense',
    amount: 8500,
    description: '技术转让项目出差费',
    date: generateDate(7),
    caseId: '',
    caseTitle: '',
    contractId: '8',
    contractTitle: '技术转让合同',
    createdAt: generateDate(7)
  },
  {
    id: '16',
    type: 'income',
    category: 'service_fee',
    amount: 75000,
    description: '供应链管理法律服务费',
    date: generateDate(35),
    caseId: '',
    caseTitle: '',
    contractId: '9',
    contractTitle: '供应链管理服务合同',
    createdAt: generateDate(35)
  },
  {
    id: '17',
    type: 'expense',
    category: 'software_license',
    amount: 20000,
    description: '法律数据库年费',
    date: generateDate(60),
    caseId: '',
    caseTitle: '',
    contractId: '',
    contractTitle: '',
    createdAt: generateDate(60)
  },
  {
    id: '18',
    type: 'income',
    category: 'consulting_fee',
    amount: 32000,
    description: '数据处理服务咨询费',
    date: generateDate(400),
    caseId: '',
    caseTitle: '',
    contractId: '10',
    contractTitle: '数据处理服务协议',
    createdAt: generateDate(400)
  }
]

// 模拟用户数据（扩展）
export const mockUserList = [
  {
    id: '1',
    username: 'admin',
    realName: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138001',
    avatar: '',
    role: 'admin',
    department: '管理部',
    status: 'active',
    createdAt: generateDate(365),
    updatedAt: generateDate(1)
  },
  {
    id: '2',
    username: 'lawyer',
    realName: '张律师',
    email: '<EMAIL>',
    phone: '13800138002',
    avatar: '',
    role: 'lawyer',
    department: '法务部',
    status: 'active',
    createdAt: generateDate(200),
    updatedAt: generateDate(10)
  },
  {
    id: '3',
    username: 'assistant',
    realName: '李助理',
    email: '<EMAIL>',
    phone: '13800138003',
    avatar: '',
    role: 'assistant',
    department: '法务部',
    status: 'active',
    createdAt: generateDate(150),
    updatedAt: generateDate(5)
  },
  {
    id: '4',
    username: 'user',
    realName: '王用户',
    email: '<EMAIL>',
    phone: '13800138004',
    avatar: '',
    role: 'user',
    department: '业务部',
    status: 'active',
    createdAt: generateDate(100),
    updatedAt: generateDate(3)
  },
  {
    id: '5',
    username: 'lawyer2',
    realName: '赵律师',
    email: '<EMAIL>',
    phone: '13800138005',
    avatar: '',
    role: 'lawyer',
    department: '法务部',
    status: 'active',
    createdAt: generateDate(180),
    updatedAt: generateDate(7)
  }
]
