// 模拟用户数据
const mockUsers = [
  {
    id: '1',
    username: 'admin',
    password: '123456',
    realName: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138001',
    avatar: '',
    role: 'admin',
    department: '管理部',
    status: 'active',
    token: 'mock-admin-token-' + Date.now(),
    permissions: [
      'user:read', 'user:write', 'user:delete',
      'case:read', 'case:write', 'case:delete',
      'contract:read', 'contract:write', 'contract:delete',
      'client:read', 'client:write', 'client:delete',
      'document:read', 'document:write', 'document:delete',
      'finance:read', 'finance:write', 'finance:delete',
      'system:read', 'system:write'
    ]
  },
  {
    id: '2',
    username: 'lawyer',
    password: '123456',
    realName: '张律师',
    email: '<EMAIL>',
    phone: '13800138002',
    avatar: '',
    role: 'lawyer',
    department: '法务部',
    status: 'active',
    token: 'mock-lawyer-token-' + Date.now(),
    permissions: [
      'case:read', 'case:write',
      'contract:read', 'contract:write',
      'client:read', 'client:write',
      'document:read', 'document:write',
      'finance:read'
    ]
  },
  {
    id: '3',
    username: 'assistant',
    password: '123456',
    realName: '李助理',
    email: '<EMAIL>',
    phone: '13800138003',
    avatar: '',
    role: 'assistant',
    department: '法务部',
    status: 'active',
    token: 'mock-assistant-token-' + Date.now(),
    permissions: [
      'case:read',
      'contract:read',
      'client:read', 'client:write',
      'document:read', 'document:write'
    ]
  },
  {
    id: '4',
    username: 'user',
    password: '123456',
    realName: '王用户',
    email: '<EMAIL>',
    phone: '13800138004',
    avatar: '',
    role: 'user',
    department: '业务部',
    status: 'active',
    token: 'mock-user-token-' + Date.now(),
    permissions: [
      'case:read',
      'contract:read',
      'client:read',
      'document:read'
    ]
  }
]

// 模拟登录API
export const mockLogin = (username: string, password: string) => {
  return new Promise((resolve, reject) => {
    // 模拟网络延迟
    setTimeout(() => {
      const user = mockUsers.find(u => u.username === username && u.password === password)
      
      if (user) {
        resolve({
          code: 200,
          data: {
            token: user.token,
            user: {
              id: user.id,
              username: user.username,
              realName: user.realName,
              email: user.email,
              phone: user.phone,
              avatar: user.avatar,
              role: user.role,
              department: user.department,
              status: user.status
            },
            permissions: user.permissions
          },
          message: '登录成功'
        })
      } else {
        reject({
          code: 401,
          message: '用户名或密码错误',
          data: null
        })
      }
    }, 800) // 模拟800ms网络延迟
  })
}

// 模拟获取用户信息API
export const mockGetUserInfo = (token: string) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const user = mockUsers.find(u => u.token === token)
      
      if (user) {
        resolve({
          code: 200,
          data: {
            user: {
              id: user.id,
              username: user.username,
              realName: user.realName,
              email: user.email,
              phone: user.phone,
              avatar: user.avatar,
              role: user.role,
              department: user.department,
              status: user.status
            },
            permissions: user.permissions
          },
          message: '获取用户信息成功'
        })
      } else {
        reject({
          code: 401,
          message: 'Token无效',
          data: null
        })
      }
    }, 300)
  })
}

// 模拟登出API
export const mockLogout = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: null,
        message: '登出成功'
      })
    }, 300)
  })
}

// 获取所有模拟用户（用于演示）
export const getMockUsers = () => {
  return mockUsers.map(user => ({
    username: user.username,
    password: user.password,
    realName: user.realName,
    role: user.role
  }))
}
