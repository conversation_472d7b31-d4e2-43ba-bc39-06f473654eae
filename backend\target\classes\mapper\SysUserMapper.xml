<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudlegal.mapper.SysUserMapper">

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" resultType="com.cloudlegal.entity.SysUser">
        SELECT u.*, r.role_name
        FROM sys_user u
        LEFT JOIN sys_role r ON u.role_id = r.id
        WHERE u.username = #{username}
        AND u.deleted = 0
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" resultType="com.cloudlegal.entity.SysUser">
        SELECT u.*, r.role_name
        FROM sys_user u
        LEFT JOIN sys_role r ON u.role_id = r.id
        WHERE u.email = #{email}
        AND u.deleted = 0
    </select>

    <!-- 根据手机号查询用户 -->
    <select id="selectByPhone" resultType="com.cloudlegal.entity.SysUser">
        SELECT u.*, r.role_name
        FROM sys_user u
        LEFT JOIN sys_role r ON u.role_id = r.id
        WHERE u.phone = #{phone}
        AND u.deleted = 0
    </select>

    <!-- 分页查询用户列表 -->
    <select id="selectUserPage" resultType="com.cloudlegal.entity.SysUser">
        SELECT u.*, r.role_name
        FROM sys_user u
        LEFT JOIN sys_role r ON u.role_id = r.id
        WHERE u.deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (u.username LIKE CONCAT('%', #{keyword}, '%')
            OR u.real_name LIKE CONCAT('%', #{keyword}, '%')
            OR u.email LIKE CONCAT('%', #{keyword}, '%')
            OR u.phone LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="status != null">
            AND u.status = #{status}
        </if>
        <if test="roleId != null">
            AND u.role_id = #{roleId}
        </if>
        ORDER BY u.created_time DESC
    </select>

    <!-- 查询用户列表 -->
    <select id="selectUserList" resultType="com.cloudlegal.entity.SysUser">
        SELECT u.*, r.role_name
        FROM sys_user u
        LEFT JOIN sys_role r ON u.role_id = r.id
        WHERE u.deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (u.username LIKE CONCAT('%', #{keyword}, '%')
            OR u.real_name LIKE CONCAT('%', #{keyword}, '%')
            OR u.email LIKE CONCAT('%', #{keyword}, '%')
            OR u.phone LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="status != null">
            AND u.status = #{status}
        </if>
        <if test="roleId != null">
            AND u.role_id = #{roleId}
        </if>
        ORDER BY u.created_time DESC
    </select>

    <!-- 更新用户最后登录信息 -->
    <update id="updateLastLoginInfo">
        UPDATE sys_user 
        SET last_login_time = #{loginTime}, last_login_ip = #{loginIp}
        WHERE id = #{userId}
    </update>

    <!-- 统计用户数量 -->
    <select id="countUsers" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM sys_user
        WHERE deleted = 0
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

    <!-- 根据角色ID查询用户数量 -->
    <select id="countUsersByRoleId" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM sys_user
        WHERE role_id = #{roleId}
        AND deleted = 0
    </select>

    <!-- 根据部门ID查询用户列表 -->
    <select id="selectUsersByDeptId" resultType="com.cloudlegal.entity.SysUser">
        SELECT u.*, r.role_name
        FROM sys_user u
        LEFT JOIN sys_role r ON u.role_id = r.id
        WHERE u.dept_id = #{deptId}
        AND u.deleted = 0
        ORDER BY u.created_time DESC
    </select>

    <!-- 批量更新用户状态 -->
    <update id="batchUpdateStatus">
        UPDATE sys_user 
        SET status = #{status}
        WHERE id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

</mapper>
