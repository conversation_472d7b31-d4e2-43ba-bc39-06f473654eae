#!/bin/bash

echo "========================================"
echo "Maven 打包脚本"
echo "========================================"

echo "请选择打包方式:"
echo "[1] 生产环境(跳过测试)"
echo "[2] 开发环境(包含测试)"
echo "[3] 清理项目"
echo "[4] 快速打包(跳过测试和文档)"

read -p "请输入选择 [1-4]: " choice

case $choice in
    1)
        echo "开始生产环境打包..."
        echo "跳过测试文件"
        mvn clean package -Pprod -DskipTests=true -Dmaven.test.skip=true
        echo "生产环境打包完成！"
        ;;
    2)
        echo "开始开发环境打包..."
        echo "包含测试文件"
        mvn clean package -Pdev
        echo "开发环境打包完成！"
        ;;
    3)
        echo "清理项目..."
        mvn clean
        echo "清理完成！"
        ;;
    4)
        echo "快速打包..."
        echo "跳过测试和文档生成"
        mvn clean package -DskipTests=true -Dmaven.test.skip=true -Dmaven.javadoc.skip=true
        echo "快速打包完成！"
        ;;
    *)
        echo "无效选择，默认使用生产环境打包"
        mvn clean package -Pprod -DskipTests=true -Dmaven.test.skip=true
        echo "生产环境打包完成！"
        ;;
esac
