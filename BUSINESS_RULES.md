# 云法务系统业务规则详细说明

## 📋 文档说明

本文档详细描述了云法务系统中所有业务模块的判定规则、验证规则和业务逻辑，为开发、测试和运维提供准确的业务规范。

## 🔐 用户认证与权限规则

### 用户注册规则

#### 用户名验证规则
```javascript
function validateUsername(username) {
    // 规则1: 长度限制
    if (username.length < 3 || username.length > 50) {
        return "用户名长度必须在3-50个字符之间";
    }
    
    // 规则2: 字符限制
    const pattern = /^[a-zA-Z0-9_]+$/;
    if (!pattern.test(username)) {
        return "用户名只能包含字母、数字和下划线";
    }
    
    // 规则3: 唯一性检查
    if (isUsernameExists(username)) {
        return "用户名已存在";
    }
    
    // 规则4: 保留字检查
    const reservedWords = ['admin', 'root', 'system', 'null', 'undefined'];
    if (reservedWords.includes(username.toLowerCase())) {
        return "用户名不能使用系统保留字";
    }
    
    return null; // 验证通过
}
```

#### 密码强度规则
```javascript
function validatePassword(password) {
    // 规则1: 长度限制
    if (password.length < 6 || password.length > 100) {
        return "密码长度必须在6-100个字符之间";
    }
    
    // 规则2: 复杂度要求(可选)
    const hasLetter = /[a-zA-Z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    
    let complexity = 0;
    if (hasLetter) complexity++;
    if (hasNumber) complexity++;
    if (hasSpecial) complexity++;
    
    if (complexity < 2) {
        return "密码必须包含字母、数字、特殊字符中的至少两种";
    }
    
    // 规则3: 常见密码检查
    const commonPasswords = ['123456', 'password', 'admin', '111111'];
    if (commonPasswords.includes(password.toLowerCase())) {
        return "不能使用常见密码";
    }
    
    return null; // 验证通过
}
```

### 登录控制规则

#### 登录失败锁定规则
```javascript
function checkLoginAttempts(username) {
    const attempts = getFailedAttempts(username);
    const lockTime = getLockTime(username);
    
    // 规则1: 失败次数检查
    if (attempts >= 5) {
        const remainingTime = lockTime + (30 * 60 * 1000) - Date.now();
        if (remainingTime > 0) {
            return {
                locked: true,
                message: `账户已锁定，请${Math.ceil(remainingTime/60000)}分钟后重试`
            };
        } else {
            // 锁定时间已过，重置失败次数
            resetFailedAttempts(username);
        }
    }
    
    return { locked: false };
}
```

#### 会话管理规则
```javascript
function validateSession(token) {
    // 规则1: Token格式验证
    if (!isValidJWTFormat(token)) {
        return { valid: false, reason: "Token格式无效" };
    }
    
    // 规则2: Token过期检查
    const payload = decodeJWT(token);
    if (payload.exp < Date.now() / 1000) {
        return { valid: false, reason: "Token已过期" };
    }
    
    // 规则3: 用户状态检查
    const user = getUserById(payload.userId);
    if (!user || user.status !== 1) {
        return { valid: false, reason: "用户状态异常" };
    }
    
    // 规则4: 权限检查
    const permissions = getUserPermissions(payload.userId);
    return { 
        valid: true, 
        userId: payload.userId,
        permissions: permissions
    };
}
```

## 👥 客户管理业务规则

### 客户信息验证规则

#### 企业客户验证
```javascript
function validateCorporateClient(client) {
    const errors = [];
    
    // 规则1: 必填字段检查
    const requiredFields = ['client_name', 'credit_code', 'legal_person', 'contact_person', 'phone'];
    requiredFields.forEach(field => {
        if (!client[field] || client[field].trim() === '') {
            errors.push(`${getFieldName(field)}不能为空`);
        }
    });
    
    // 规则2: 统一社会信用代码验证
    if (client.credit_code) {
        if (!isValidCreditCode(client.credit_code)) {
            errors.push("统一社会信用代码格式不正确");
        }
        
        // 唯一性检查
        if (isCreditCodeExists(client.credit_code, client.id)) {
            errors.push("统一社会信用代码已存在");
        }
    }
    
    // 规则3: 注册资本验证
    if (client.registered_capital) {
        if (client.registered_capital <= 0) {
            errors.push("注册资本必须大于0");
        }
        if (client.registered_capital > 999999999999.99) {
            errors.push("注册资本不能超过999999999999.99");
        }
    }
    
    return errors;
}
```

#### 个人客户验证
```javascript
function validateIndividualClient(client) {
    const errors = [];
    
    // 规则1: 必填字段检查
    const requiredFields = ['client_name', 'phone'];
    requiredFields.forEach(field => {
        if (!client[field] || client[field].trim() === '') {
            errors.push(`${getFieldName(field)}不能为空`);
        }
    });
    
    // 规则2: 身份证号验证(如果提供)
    if (client.id_card) {
        if (!isValidIdCard(client.id_card)) {
            errors.push("身份证号格式不正确");
        }
    }
    
    return errors;
}
```

### 客户状态管理规则

#### 客户状态转换规则
```javascript
function validateClientStatusChange(currentStatus, newStatus, clientId) {
    // 规则1: 状态转换矩阵
    const allowedTransitions = {
        0: [1],      // 停用 -> 正常
        1: [0, 2],   // 正常 -> 停用/注销
        2: []        // 注销 -> 无法转换
    };
    
    if (!allowedTransitions[currentStatus].includes(newStatus)) {
        return "不允许的状态转换";
    }
    
    // 规则2: 业务约束检查
    if (newStatus === 2) { // 注销状态
        const activeCases = getActiveCasesByClient(clientId);
        if (activeCases.length > 0) {
            return "客户存在进行中的案件，无法注销";
        }
        
        const activeContracts = getActiveContractsByClient(clientId);
        if (activeContracts.length > 0) {
            return "客户存在有效合同，无法注销";
        }
    }
    
    return null; // 验证通过
}
```

## ⚖️ 案件管理业务规则

### 案件创建规则

#### 案件编号生成规则
```javascript
function generateCaseNumber(caseType, clientId) {
    // 规则: CASE + 年份 + 案件类型代码 + 流水号
    const year = new Date().getFullYear();
    const typeCode = getCaseTypeCode(caseType);
    const sequence = getNextSequence('case', year);
    
    return `CASE${year}${typeCode}${sequence.toString().padStart(4, '0')}`;
}

function getCaseTypeCode(caseType) {
    const typeCodes = {
        'civil': 'C',        // 民事
        'criminal': 'X',     // 刑事
        'administrative': 'A', // 行政
        'corporate': 'G',    // 公司
        'intellectual': 'Z', // 知识产权
        'labor': 'L'         // 劳动
    };
    return typeCodes[caseType] || 'O'; // 其他
}
```

#### 案件优先级自动判定
```javascript
function determineCasePriority(caseData) {
    let priority = 'low'; // 默认低优先级
    
    // 规则1: 金额判定
    if (caseData.case_amount >= 10000000) {
        priority = 'urgent';
    } else if (caseData.case_amount >= 5000000) {
        priority = 'high';
    } else if (caseData.case_amount >= 1000000) {
        priority = 'medium';
    }
    
    // 规则2: 案件类型判定
    const urgentTypes = ['criminal', 'administrative'];
    if (urgentTypes.includes(caseData.case_type)) {
        priority = priority === 'low' ? 'high' : priority;
    }
    
    // 规则3: 客户重要性判定
    const client = getClientById(caseData.client_id);
    if (client && client.importance_level === 'vip') {
        priority = priority === 'low' ? 'medium' : 
                  priority === 'medium' ? 'high' : 
                  priority === 'high' ? 'urgent' : priority;
    }
    
    // 规则4: 时间紧迫性判定
    if (caseData.deadline) {
        const daysToDeadline = getDaysDifference(new Date(), caseData.deadline);
        if (daysToDeadline <= 7) {
            priority = 'urgent';
        } else if (daysToDeadline <= 30) {
            priority = priority === 'low' ? 'medium' : priority;
        }
    }
    
    return priority;
}
```

### 案件分配规则

#### 律师自动分配算法
```javascript
function assignLawyer(caseData) {
    // 规则1: 专业匹配
    const specializedLawyers = getLawyersBySpecialty(caseData.case_type);
    if (specializedLawyers.length === 0) {
        throw new Error("没有匹配的专业律师");
    }
    
    // 规则2: 工作负荷平衡
    const lawyerWorkloads = specializedLawyers.map(lawyer => ({
        lawyer: lawyer,
        workload: getActiveCaseCount(lawyer.id),
        capacity: lawyer.max_cases || 20
    }));
    
    // 过滤掉已满负荷的律师
    const availableLawyers = lawyerWorkloads.filter(item => 
        item.workload < item.capacity
    );
    
    if (availableLawyers.length === 0) {
        throw new Error("所有专业律师都已满负荷");
    }
    
    // 规则3: 选择工作负荷最轻的律师
    const selectedLawyer = availableLawyers.reduce((min, current) => 
        current.workload < min.workload ? current : min
    );
    
    // 规则4: VIP客户优先分配资深律师
    const client = getClientById(caseData.client_id);
    if (client.importance_level === 'vip') {
        const seniorLawyers = availableLawyers.filter(item => 
            item.lawyer.level === 'senior'
        );
        if (seniorLawyers.length > 0) {
            return seniorLawyers[0].lawyer;
        }
    }
    
    return selectedLawyer.lawyer;
}
```

### 案件状态流转规则

#### 状态转换验证
```javascript
function validateCaseStatusChange(caseId, currentStatus, newStatus, userId) {
    // 规则1: 状态转换矩阵
    const statusTransitions = {
        'pending': ['processing', 'cancelled'],
        'processing': ['suspended', 'completed', 'cancelled'],
        'suspended': ['processing', 'cancelled'],
        'completed': ['archived'],
        'cancelled': [],
        'archived': []
    };
    
    if (!statusTransitions[currentStatus].includes(newStatus)) {
        return "不允许的状态转换";
    }
    
    // 规则2: 权限检查
    const userRole = getUserRole(userId);
    const caseData = getCaseById(caseId);
    
    if (newStatus === 'completed') {
        // 只有负责律师或管理员可以结案
        if (caseData.lawyer_id !== userId && !['admin', 'manager'].includes(userRole)) {
            return "只有负责律师或管理员可以结案";
        }
        
        // 结案必须填写结案报告
        if (!caseData.completion_report) {
            return "结案前必须填写结案报告";
        }
    }
    
    if (newStatus === 'cancelled') {
        // 取消案件需要管理员权限
        if (!['admin', 'manager'].includes(userRole)) {
            return "只有管理员可以取消案件";
        }
        
        // 取消必须填写取消原因
        if (!caseData.cancellation_reason) {
            return "取消案件必须填写取消原因";
        }
    }
    
    return null; // 验证通过
}
```

## 📄 合同管理业务规则

### 合同编号生成规则

```javascript
function generateContractNumber(contractType, clientId) {
    // 规则: CT + 年份 + 合同类型代码 + 流水号
    const year = new Date().getFullYear();
    const typeCode = getContractTypeCode(contractType);
    const sequence = getNextSequence('contract', year);
    
    return `CT${year}${typeCode}${sequence.toString().padStart(4, '0')}`;
}

function getContractTypeCode(contractType) {
    const typeCodes = {
        'service': 'S',      // 服务合同
        'sales': 'SA',       // 买卖合同
        'lease': 'L',        // 租赁合同
        'employment': 'E',   // 劳动合同
        'partnership': 'P',  // 合作协议
        'license': 'LI',     // 许可协议
        'consulting': 'C'    // 咨询合同
    };
    return typeCodes[contractType] || 'O'; // 其他
}
```

### 合同审批流程规则

#### 审批流程判定
```javascript
function determineApprovalFlow(contractData) {
    const amount = contractData.contract_amount || 0;
    const type = contractData.contract_type;
    const clientLevel = getClientImportanceLevel(contractData.client_id);
    
    // 规则1: 金额审批流程
    if (amount >= 1000000) {
        return ['legal_review', 'business_confirm', 'ceo_approval', 'board_approval'];
    } else if (amount >= 500000) {
        return ['legal_review', 'business_confirm', 'ceo_approval'];
    } else if (amount >= 100000) {
        return ['legal_review', 'manager_approval'];
    } else {
        return ['legal_review'];
    }
    
    // 规则2: 特殊类型合同
    const specialTypes = ['partnership', 'license'];
    if (specialTypes.includes(type)) {
        return ['legal_review', 'business_confirm', 'ceo_approval'];
    }
    
    // 规则3: VIP客户合同
    if (clientLevel === 'vip') {
        return ['legal_review', 'business_confirm', 'manager_approval'];
    }
}
```

### 合同到期提醒规则

#### 到期提醒计算
```javascript
function calculateContractReminders(contract) {
    const endDate = new Date(contract.end_date);
    const today = new Date();
    const daysToExpire = getDaysDifference(today, endDate);
    
    const reminders = [];
    
    // 规则1: 标准提醒时间点
    const reminderDays = [30, 15, 7, 1];
    
    reminderDays.forEach(days => {
        if (daysToExpire === days) {
            reminders.push({
                type: 'expire_reminder',
                days: days,
                urgency: days <= 7 ? 'high' : 'normal',
                message: `合同将在${days}天后到期`
            });
        }
    });
    
    // 规则2: 已过期提醒
    if (daysToExpire < 0) {
        reminders.push({
            type: 'expired',
            days: Math.abs(daysToExpire),
            urgency: 'urgent',
            message: `合同已过期${Math.abs(daysToExpire)}天`
        });
    }
    
    // 规则3: 重要合同特殊提醒
    if (contract.contract_amount >= 1000000) {
        const specialReminderDays = [60, 45];
        specialReminderDays.forEach(days => {
            if (daysToExpire === days) {
                reminders.push({
                    type: 'important_contract_reminder',
                    days: days,
                    urgency: 'high',
                    message: `重要合同将在${days}天后到期，请及时处理`
                });
            }
        });
    }
    
    return reminders;
}
```

## 💰 财务管理业务规则

### 收支记录验证规则

#### 收入记录验证
```javascript
function validateIncomeRecord(record) {
    const errors = [];
    
    // 规则1: 必填字段检查
    if (!record.amount || record.amount <= 0) {
        errors.push("收入金额必须大于0");
    }
    
    if (!record.category) {
        errors.push("收入类别不能为空");
    }
    
    if (!record.record_date) {
        errors.push("记录日期不能为空");
    }
    
    // 规则2: 金额合理性检查
    if (record.amount > 10000000) {
        errors.push("单笔收入金额不能超过1000万元");
    }
    
    // 规则3: 日期合理性检查
    const recordDate = new Date(record.record_date);
    const today = new Date();
    const oneYearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
    
    if (recordDate > today) {
        errors.push("记录日期不能是未来日期");
    }
    
    if (recordDate < oneYearAgo) {
        errors.push("记录日期不能超过一年前");
    }
    
    // 规则4: 业务关联检查
    if (record.case_id) {
        const caseData = getCaseById(record.case_id);
        if (!caseData) {
            errors.push("关联的案件不存在");
        } else if (caseData.case_status === 'cancelled') {
            errors.push("不能关联已取消的案件");
        }
    }
    
    if (record.contract_id) {
        const contractData = getContractById(record.contract_id);
        if (!contractData) {
            errors.push("关联的合同不存在");
        }
    }
    
    return errors;
}
```

#### 支出审批规则
```javascript
function validateExpenseApproval(expense, approver) {
    const amount = expense.amount;
    const approverRole = getUserRole(approver.id);
    const approverLevel = getApprovalLevel(approverRole);
    
    // 规则1: 审批权限矩阵
    const approvalLimits = {
        'assistant': 0,
        'lawyer': 1000,
        'manager': 5000,
        'director': 20000,
        'ceo': 100000,
        'board': Infinity
    };
    
    const maxAmount = approvalLimits[approverLevel] || 0;
    
    if (amount > maxAmount) {
        return `您的审批权限不足，最大可审批金额为${maxAmount}元`;
    }
    
    // 规则2: 特殊类别审批
    const restrictedCategories = ['travel_expense', 'entertainment'];
    if (restrictedCategories.includes(expense.category)) {
        if (approverLevel === 'lawyer') {
            return "此类费用需要经理级别以上审批";
        }
    }
    
    // 规则3: 连续审批检查
    const recentApprovals = getRecentApprovalsByUser(approver.id, 24); // 24小时内
    const totalRecentAmount = recentApprovals.reduce((sum, item) => sum + item.amount, 0);
    
    if (totalRecentAmount + amount > maxAmount * 2) {
        return "24小时内审批总金额超出限制";
    }
    
    return null; // 审批通过
}
```

### 财务统计规则

#### 收支统计计算
```javascript
function calculateFinancialStats(startDate, endDate) {
    const records = getFinanceRecords(startDate, endDate);
    
    const stats = {
        totalIncome: 0,
        totalExpense: 0,
        netProfit: 0,
        profitMargin: 0,
        categoryBreakdown: {},
        monthlyTrend: []
    };
    
    // 规则1: 收支分类统计
    records.forEach(record => {
        if (record.record_type === 1) { // 收入
            stats.totalIncome += record.amount;
        } else { // 支出
            stats.totalExpense += record.amount;
        }
        
        // 分类统计
        if (!stats.categoryBreakdown[record.category]) {
            stats.categoryBreakdown[record.category] = {
                income: 0,
                expense: 0
            };
        }
        
        if (record.record_type === 1) {
            stats.categoryBreakdown[record.category].income += record.amount;
        } else {
            stats.categoryBreakdown[record.category].expense += record.amount;
        }
    });
    
    // 规则2: 利润计算
    stats.netProfit = stats.totalIncome - stats.totalExpense;
    stats.profitMargin = stats.totalIncome > 0 ? 
        (stats.netProfit / stats.totalIncome * 100).toFixed(2) : 0;
    
    // 规则3: 月度趋势分析
    const monthlyData = groupRecordsByMonth(records);
    stats.monthlyTrend = Object.keys(monthlyData).map(month => ({
        month: month,
        income: monthlyData[month].income,
        expense: monthlyData[month].expense,
        profit: monthlyData[month].income - monthlyData[month].expense
    }));
    
    return stats;
}
```

## 🤖 智能服务规则

### 智能咨询匹配规则

#### 问题分类算法
```javascript
function classifyQuestion(question) {
    const keywords = {
        'legal_article': ['法条', '法律条文', '法规', '条例', '规定'],
        'case_inquiry': ['案例', '判决', '类似', '先例', '参考'],
        'procedure': ['流程', '程序', '如何办理', '怎么做', '步骤'],
        'fee_inquiry': ['费用', '收费', '多少钱', '价格', '成本'],
        'time_limit': ['期限', '时间', '多久', '什么时候', '截止'],
        'document': ['文件', '材料', '证据', '文书', '表格']
    };
    
    const scores = {};
    
    // 规则1: 关键词匹配评分
    Object.keys(keywords).forEach(category => {
        scores[category] = 0;
        keywords[category].forEach(keyword => {
            if (question.includes(keyword)) {
                scores[category] += 1;
            }
        });
    });
    
    // 规则2: 选择最高分类别
    const maxScore = Math.max(...Object.values(scores));
    if (maxScore === 0) {
        return 'general'; // 通用问题
    }
    
    const bestCategory = Object.keys(scores).find(key => scores[key] === maxScore);
    
    // 规则3: 置信度计算
    const confidence = maxScore / keywords[bestCategory].length;
    
    return {
        category: bestCategory,
        confidence: confidence,
        needsHumanReview: confidence < 0.3
    };
}
```

### 文书生成规则

#### 模板选择规则
```javascript
function selectDocumentTemplate(documentType, caseData) {
    // 规则1: 基础模板映射
    const baseTemplates = {
        'complaint': 'civil_complaint_template',
        'answer': 'civil_answer_template',
        'appeal': 'appeal_template',
        'lawyer_letter': 'lawyer_letter_template'
    };
    
    let templateId = baseTemplates[documentType];
    
    // 规则2: 案件类型细化
    if (documentType === 'complaint') {
        switch (caseData.case_type) {
            case 'criminal':
                templateId = 'criminal_complaint_template';
                break;
            case 'administrative':
                templateId = 'administrative_complaint_template';
                break;
            case 'labor':
                templateId = 'labor_complaint_template';
                break;
        }
    }
    
    // 规则3: 地区定制
    const clientRegion = getClientRegion(caseData.client_id);
    if (clientRegion && hasRegionalTemplate(templateId, clientRegion)) {
        templateId = `${templateId}_${clientRegion}`;
    }
    
    // 规则4: 模板版本选择
    const template = getLatestTemplate(templateId);
    if (!template) {
        throw new Error(`未找到模板: ${templateId}`);
    }
    
    return template;
}
```

## 📊 数据验证通用规则

### 通用字段验证

#### 手机号验证
```javascript
function validatePhone(phone) {
    if (!phone) return null;
    
    // 规则1: 格式验证
    const phonePattern = /^1[3-9]\d{9}$/;
    if (!phonePattern.test(phone)) {
        return "手机号格式不正确";
    }
    
    // 规则2: 运营商验证
    const validPrefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                          '147', '150', '151', '152', '153', '155', '156', '157', '158', '159',
                          '180', '181', '182', '183', '184', '185', '186', '187', '188', '189',
                          '170', '171', '172', '173', '174', '175', '176', '177', '178', '179'];
    
    const prefix = phone.substring(0, 3);
    if (!validPrefixes.includes(prefix)) {
        return "手机号运营商不支持";
    }
    
    return null;
}
```

#### 邮箱验证
```javascript
function validateEmail(email) {
    if (!email) return null;
    
    // 规则1: 基础格式验证
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(email)) {
        return "邮箱格式不正确";
    }
    
    // 规则2: 长度限制
    if (email.length > 100) {
        return "邮箱长度不能超过100个字符";
    }
    
    // 规则3: 域名黑名单检查
    const blacklistedDomains = ['tempmail.com', '10minutemail.com'];
    const domain = email.split('@')[1].toLowerCase();
    if (blacklistedDomains.includes(domain)) {
        return "不支持临时邮箱";
    }
    
    return null;
}
```

#### 金额验证
```javascript
function validateAmount(amount, fieldName = '金额') {
    if (amount === null || amount === undefined) {
        return null;
    }
    
    // 规则1: 数值类型检查
    if (typeof amount !== 'number' || isNaN(amount)) {
        return `${fieldName}必须是有效数字`;
    }
    
    // 规则2: 正数检查
    if (amount < 0) {
        return `${fieldName}不能为负数`;
    }
    
    // 规则3: 精度检查
    const decimalPlaces = (amount.toString().split('.')[1] || '').length;
    if (decimalPlaces > 2) {
        return `${fieldName}最多保留2位小数`;
    }
    
    // 规则4: 最大值检查
    if (amount > 999999999999.99) {
        return `${fieldName}不能超过999999999999.99`;
    }
    
    return null;
}
```

## 🔄 业务流程控制规则

### 工作流状态机

#### 通用状态转换验证
```javascript
function validateStateTransition(entity, currentState, newState, context) {
    // 规则1: 获取状态转换配置
    const transitions = getStateTransitions(entity);
    if (!transitions[currentState] || !transitions[currentState].includes(newState)) {
        return `不允许从${currentState}状态转换到${newState}状态`;
    }
    
    // 规则2: 前置条件检查
    const preconditions = getStatePreconditions(entity, newState);
    for (const condition of preconditions) {
        const result = evaluateCondition(condition, context);
        if (!result.passed) {
            return result.message;
        }
    }
    
    // 规则3: 权限检查
    const requiredPermission = getStateChangePermission(entity, newState);
    if (!hasPermission(context.userId, requiredPermission)) {
        return "没有权限执行此状态转换";
    }
    
    return null; // 验证通过
}
```

### 自动化触发规则

#### 定时任务规则
```javascript
function getScheduledTasks() {
    return [
        {
            name: 'contract_expiry_reminder',
            schedule: '0 9 * * *', // 每天9点执行
            action: () => {
                const expiringContracts = getExpiringContracts([30, 15, 7, 1]);
                expiringContracts.forEach(contract => {
                    sendExpiryReminder(contract);
                });
            }
        },
        {
            name: 'case_overdue_check',
            schedule: '0 10 * * *', // 每天10点执行
            action: () => {
                const overdueCases = getOverdueCases();
                overdueCases.forEach(caseItem => {
                    sendOverdueAlert(caseItem);
                });
            }
        },
        {
            name: 'financial_report_generation',
            schedule: '0 8 1 * *', // 每月1号8点执行
            action: () => {
                generateMonthlyFinancialReport();
            }
        }
    ];
}
```

---

**文档版本**: v1.0.0  
**创建日期**: 2024-01-01  
**适用系统**: 云法务管理系统 v1.0.0  
**文档类型**: 业务规则说明书
